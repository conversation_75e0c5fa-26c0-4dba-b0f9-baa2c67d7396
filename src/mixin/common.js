import Cookies from 'js-cookie'
import { getStorageItem } from '@/utils/storage'
import { executeInterface } from '@/api/interfaces/interfaces'

export default {
  data () {
    return {
    }
  },
  computed: {
    // 网页高度
    bodyWidth () {
      return document.body.clientWidth
    },
    // 网页宽度
    bodyHeight () {
      return document.body.clientHeight
    },
  },
  created () {
  },
  mounted () {
  },
  destroyed () {
  },
  methods: {
    // 查询echarts 数据
    queryEchartsData(params) {
      return new Promise(async (resolve) => {
        const {code, data} = await executeInterface({apiId:params.api});
        const analysisData = this.analysisChartsData(params, data.body.data);
        resolve(analysisData)
      })
    },
    // 解析不同图标的数据
    analysisChartsData(params, data) {
      // widget-barchart 柱线图、widget-linechart 折线图、 widget-barlinechart 柱线图
      // widget-piechart 饼图、widget-funnel 漏斗图
      // widget-text 文本框
      // widge-table 表格(数据不要转)
      // widget-stackchart 堆叠图
      // widget-heatmap 热力图
      // widget-mapline 中国地图-路线图
      const chartType = params.chartType
      if (
        chartType == "widget-barchart" ||
        chartType == "widget-linechart" ||
        chartType == "widget-barlinechart"
      ) {
        return this.barOrLineChartFn(params.chartProperties, data);
      } else if (
        chartType == "widget-piechart" ||
        chartType == "widget-funnel"
      ) {
        return this.piechartFn(params.chartProperties, data);
      } else if (chartType == "widget-text") {
        return this.widgettext(params.chartProperties, data)
      } else if (chartType == "widget-stackchart") {
        return this.stackChartFn(params.chartProperties, data)
      } else if (chartType == "widget-coord") {
        return this.coordChartFn(params.chartProperties, data)
      } else if (chartType == "widget-linemap") {
        return this.linemapChartFn(params.chartProperties, data)
      } else {
        return data
      }
    },
    // 柱状图、折线图、柱线图
    barOrLineChartFn(chartProperties, data) {
      const ananysicData = {};
      const xAxisList = [];
      const series = [];
      for (const key in chartProperties) {
        const obj = {};
        const seriesData = [];
        const value = chartProperties[key];
        obj["type"] = value;
        obj["name"] = key;
        for (let i = 0; i < data.length; i++) {
          if (value.startsWith("xAxis")) {
            // 代表为x轴
            xAxisList[i] = data[i][key];
          } else {
            // 其他的均为series展示数据
            seriesData[i] = data[i][key];
          }
        }
        obj["data"] = seriesData;
        if (!obj["type"].startsWith("xAxis")) {
          series.push(obj);
        }
      }
      let arr=[]
      data.forEach((item,index)=>{
        if(item.color){
          arr[index]=item.color
        }
      })
      ananysicData["xAxis"] = xAxisList;
      ananysicData["series"] = series;
      ananysicData["colorArr"] = arr;
      return ananysicData;
    },
    //堆叠图
    stackChartFn(chartProperties, data) {
      const ananysicData = {};
      const series = [];
      //全部字段字典值
      const types = Object.values(chartProperties)
      //x轴字段、y轴字段名
      const xAxisField = Object.keys(chartProperties)[types.indexOf('xAxis')]
      const yAxisField = Object.keys(chartProperties)[types.indexOf('yAxis')]
      const dataField = Object.keys(chartProperties)[types.indexOf('bar')]
      //x轴数值去重，y轴去重
      const xAxisList = this.setUnique(data.map(item => item[xAxisField]))
      const yAxisList = this.setUnique(data.map(item => item[yAxisField]))
      const dataGroup = this.setGroupBy(data, yAxisField)
      for (const key in chartProperties) {
        if (chartProperties[key] !== 'yAxis' && !chartProperties[key].startsWith('xAxis')) {
          Object.keys(dataGroup).forEach(item => {
            const data = new Array(xAxisList.length).fill(0)
            dataGroup[item].forEach(res => {
              data[xAxisList.indexOf(res[xAxisField])] = res[key]
            })
            series.push({
              name: yAxisList[item],
              type: chartProperties[key],
              data: data,
            })
          })
        }
      }
      ananysicData["xAxis"] = xAxisList;
      ananysicData["series"] = series;
      return ananysicData;
    },
    // 饼图、漏斗图
    piechartFn(chartProperties, data) {
      const ananysicData = [];
      for (let i = 0; i < data.length; i++) {
        const obj = {};
        for (const key in chartProperties) {
          const value = chartProperties[key];
          if (value === "name") {
            obj["name"] = data[i][key];
          } else {
            obj["value"] = data[i][key];
          }
        }
        if(data[i].color){
          obj['color']=data[i].color
        }
        ananysicData.push(obj);
      }
      return ananysicData;
    },
    widgettext(chartProperties, data) {
      // console.log(chartProperties, data)
      // const ananysicData = [];

      // for (let i = 0; i < data.length; i++) {
      //   const obj = {};
      //   for (const key in chartProperties) {
      //     const value = chartProperties[key];
      //     if (value === "name") {
      //     } else {
      //       obj["value"] = data[i][key];
      //     }
      //   }
      //   ananysicData.push(obj);
      // }
      return data;
    },
    // 坐标系数据解析
    coordChartFn(chartProperties, data) {
      const ananysicData = {};
      let series = [];
      //全部字段字典值
      const types = Object.values(chartProperties)
      //x轴字段、y轴字段、数值字段名
      const xAxisField = Object.keys(chartProperties)[types.indexOf('xAxis')]
      const yAxisField = Object.keys(chartProperties)[types.indexOf('yAxis')]
      const dataField = Object.keys(chartProperties)[types.indexOf('series')]
      //x轴数值去重，y轴去重
      const xAxisList = this.setUnique(data.map(item => item[xAxisField]))
      const yAxisList = this.setUnique(data.map(item => item[yAxisField]))
      ananysicData["xAxis"] = xAxisList;
      ananysicData["yAxis"] = yAxisList;
      for (const i in data) {
        series[i] = [data[i][xAxisField], data[i][yAxisField], data[i][dataField]];
      }
      ananysicData["series"] = series;
      return ananysicData;
    },
    // 中国地图。路线图数据解析，适合source、target、value
    linemapChartFn(chartProperties, data) {
      const ananysicData = [];
      for (let i = 0; i < data.length; i++) {
        const obj = {};
        for (const key in chartProperties) {
          const value = chartProperties[key];
          if (value === "source") {
            obj["source"] = data[i][key];
          } else if (value === "target") {
            obj["target"] = data[i][key];
          } else {
            obj["value"] = data[i][key];
          }
        }
        ananysicData.push(obj);
      }
      return ananysicData;
    },
    setUnique(arr) {
      let newArr = [];
      arr.forEach(item => {
        return newArr.includes(item) ? '' : newArr.push(item);
      });
      return newArr;
    },
    setGroupBy(array, name) {
      const groups = {}
      array.forEach(function (o) {
        const group = JSON.stringify(o[name])
        groups[group] = groups[group] || []
        groups[group].push(o)
      })
      return Object.keys(groups).map(function (group) {
        return groups[group]
      })
    },
    setCookies (key, val, option) {
      if (option == null) {
        option = { expires: 15 }
      }
      Cookies.set(key, val, option)
    },
    goBack () {
      this.$router.go(-1)
    },
    refresh () {
      this.$router.go(0)
    },
    parseString (object) {
      if (typeof object === 'undefined' || object == null) {
        return ''
      }
      if (typeof object === 'number') {
        return object.toString()
      }
      if (typeof object === 'boolean') {
        return object.toString()
      }
      if (typeof object === 'object') {
        return JSON.stringify(object)
      }
      return ''
    },

    // 封装定制删除数组中的值
    contains (a, obj) {
      let i = a.length
      while (i--) {
        if (a[i] === obj) {
          return i
        }
      }
      return false
    },
    //获取url后边参数
    getUrlKey: function (name) {
      return (
        decodeURIComponent(
          (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(
            /\+/g,
            '%20'
          )
        ) || null
      )
    },
    /**
     *
     */

    sortArray (propertyName) {
      return function (object1, object2) {
        let value1 = object1[propertyName];
        let value2 = object2[propertyName];

        if (value1 < value2) {
          return -1;
        } else if (value1 > value2) {
          return 1;
        } else {
          return 0;
        }
      }
    },
    // 获取对象类型
    getObjectType (obj) {
      let toString = Object.prototype.toString
      let map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object',
      }
      if (obj instanceof Element) {
        return 'element'
      }
      return map[toString.call(obj)]
    },
    isNumber (obj) {
      return this.getObjectType(obj) == 'number'
    },
    isString (obj) {
      return this.getObjectType(obj) == 'string'
    },
    isArray (obj) {
      return this.getObjectType(obj) == 'array'
    },
    hasOwn (obj, key) {
      return Object.prototype.hasOwnProperty.call(obj, key)
    },

    isNotBlank (val) {
      return !this.isBlank(val)
    },
    isBlank (val) {
      if (typeof val === 'undefined') {
        return true
      }
      if (this.isNull(val)) {
        return true
      }
      if (typeof val === 'string') {
        return val.trim() == ''
      }
      if (typeof val === 'object') {
        for (let key in val) {
          return false
        }
        return true
      }
      return false
    },
    isNotNull (val) {
      return !this.isNull(val)
    },
    isNull (val) {
      // 特殊判断
      if (val && parseInt(val) === 0) return false
      const list = ['$parent']
      if (val instanceof Date || typeof val === 'boolean' || typeof val === 'number') return false
      if (val instanceof Array) {
        if (val.length === 0) return true
      } else if (val instanceof Object) {
        val = this.deepClone(val)
        list.forEach((ele) => {
          delete val[ele]
        })
        for (let o in val) {
          return false
        }
        return true
      } else {
        if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '') {
          return true
        }
        return false
      }
      return false
    },

    // 对象深拷贝
    deepClone (data) {
      let type = this.getObjectType(data)
      let obj
      if (type === 'array') {
        obj = []
      } else if (type === 'object') {
        obj = {}
      } else {
        // 不再具有下一层次
        return data
      }
      if (type === 'array') {
        for (let i = 0, len = data.length; i < len; i++) {
          data[i] = (() => {
            if (data[i] === 0) {
              return data[i]
            }
            return data[i]
          })()
          if (data[i]) {
            delete data[i].$parent
          }
          obj.push(this.deepClone(data[i]))
        }
      } else if (type === 'object') {
        for (let key in data) {
          if (data) {
            delete data.$parent
          }
          obj[key] = this.deepClone(data[key])
        }
      }
      return obj
    },

    // 合并json
    mergeObject () {
      let target = arguments[0] || {}
      let deep = false
      let arr = Array.prototype.slice.call(arguments)
      let i = 1
      let options, src, key, copy
      let isArray = false
      if (typeof target === 'boolean') {
        deep = target
        i++
        target = arguments[1]
      }
      for (; i < arr.length; i++) {
        // 循环传入的对象数组
        if ((options = arr[i]) != null) {
          // 如果当前值不是null，如果是null不做处理
          for (key in options) {
            // for in循环对象中key
            copy = options[key]
            src = target[key]
            // 如果对象中value值任然是一个引用类型
            if (deep && (toString.call(copy) === '[object Object]' || (isArray = toString.call(copy) == '[object Array]'))) {
              if (isArray) {
                // 如果引用类型是数组
                // 如果目标对象target存在当前key，且数据类型是数组，那就还原此值，如果不是就定义成一个空数组;
                src = toString.call(src) === '[object Array]' ? src : []
              } else {
                // 如果目标对象target存在当前key，且数据类型是对象，那就还原此值，如果不是就定义成一个空对象;
                src = toString.call(src) === '[object Object]' ? src : {}
              }
              // 引用类型就再次调用extend，递归，直到此时copy是一个基本类型的值。
              target[key] = this.mergeObject(deep, src, copy)
            } else if (copy !== undefined && copy !== src) {
              // 如果这个值是基本值类型，且不是undefined
              target[key] = copy
            }
          }
        }
      }
      return target
    },

    // 获取dom在屏幕中的top和left
    getDomTopLeftById (id) {
      let dom = document.getElementById(id)
      let top = 0
      let left = 0
      if (dom != null) {
        top = dom.getBoundingClientRect().top
        left = dom.getBoundingClientRect().left
      }
      return { top: top, left: left }
    },
    objToOne (obj) {
      // console.log(obj)
      let tmpData = {}
      for (let index in obj) {
        if (typeof obj[index] == 'object' && !this.isArrayFn(obj[index])) {
          let resObj = this.objToOne(obj[index])
          Object.assign(tmpData, resObj) // 这里使用对象合并
        } else {
          tmpData[index] = obj[index]
        }
      }
      return tmpData
    },
    isArrayFn(value) {
      if (typeof Array.isArray === "function") {
        return Array.isArray(value);
      } else {
        return Object.prototype.toString.call(value) === "[object Array]";
      }
    },
    urlEncode (val) {
      return encodeURIComponent(val)
    },
    urlDecode (val) {
      return decodeURIComponent(val)
    },
    urlEncodeObject (obj, ingoreFields) {
      if (toString.call(obj) != '[object Object]') {
        return obj
      }
      let result = {}
      for (let key in obj) {
        if (this.isBlank(obj[key])) {
          continue
        }
        if (ingoreFields != null && ingoreFields.indexOf(key) >= 0) {
          result[key] = obj[key]
        } else {
          result[key] = this.urlEncode(obj[key])
        }
      }
      return result
    },

    // 根据数据字典，查询指定字典dict指定值code的，返回整个dictItem{id, text, extend}
    getDictItemByCode (dict, code) {
      let dicts = getStorageItem('AJReportDict')
      if (!dicts.hasOwnProperty(dict)) {
        return null
      }
      let dictItems = dicts[dict]
      for (let i = 0; i < dictItems.length; i++) {
        let dictItem = dictItems[i]
        if (typeof (code) == 'number') {
          code = code.toString()
        }
        if (dictItem['id'].toString() == code) {
          return dictItem
        }
      }
      return null
    },
    // 根据数据字典，查询指定字典dict指定值code的dictItem.text
    getDictLabelByCode (dict, code) {
      let dictItem = this.getDictItemByCode(dict, code)
      if (dictItem != null) {
        return dictItem['text']
      } else {
        return ''
      }
    },
    // 根据数据字典，查询指定字典dict指定值code的dictItem.extend
    getDictExtendByCode (dict, code) {
      let dictItem = this.getDictItemByCode(dict, code)
      if (dictItem == null) {
        return null
      }
      let extend = dictItem['extend']
      if (extend == null || extend.trim() == 'null') {
        return null
      }
      return dictItem['extend']
    },
    getSettingByName(settingName) {
      let gaeaSetting = JSON.parse(localStorage.getItem('AJReportDict'))
      if (gaeaSetting[settingName] != null) {
        return gaeaSetting[settingName]
      } else {
        // console.error('没有找到系统参数' + settingName + '，请与后端联系')
        return null
      }
    },
  }
}
