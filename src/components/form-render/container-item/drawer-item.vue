<template>
  <container-item-wrapper :widget="widget">
    <el-drawer
      :title="widget.options.label"
      :visible.sync="visible"
      :size="widget.options.width || '30%'"
      :direction="widget.options.drawerDirection">
        <template v-for="(subWidget, swIdx) in widget.widgetList">
          <template v-if="'container' === subWidget.category">
            <component :is="subWidget.type + '-item'" :widget="subWidget" :key="swIdx" :parent-list="widget.widgetList"
                       :index-of-parent-list="swIdx" :parent-widget="widget"></component>
          </template>
          <template v-else>
            <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="null" :key="swIdx" :parent-list="widget.widgetList"
                       :index-of-parent-list="swIdx"  :parent-widget="widget"></component>
          </template>
        </template>
    </el-drawer>
  </container-item-wrapper>
</template>

<script>
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n from "@/utils/i18n"
  import refMixin from "@/components/form-render/refMixin"
  import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper.vue'
  import containerItemMixin from "@/components/form-render/container-item/containerItemMixin"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

  export default {
    name: "drawer-item",
    componentName: 'ContainerItem',
    mixins: [emitter, i18n, refMixin, containerItemMixin],
    components: {
      ContainerItemWrapper,
      ...FieldComponents,
    },
    props: {
      widget: Object,
    },
    data(){
      return{
        visible:false
      }
    },
    inject: ['refList', 'sfRefList', 'globalModel'],
    computed: {
      customClass() {
        return this.widget.options.customClass || ''
      },

    },
    created() {
      this.initRefList()
    },
    beforeDestroy() {
      this.unregisterFromRefList()
    },
    methods: {
      setVisible(flag, callback){
        this.visible = flag
        if(callback){
           this.$nextTick(() =>{
          callback()
        })
        }
      },
    },
  }
</script>

<style lang="scss">
.v-modal{
  z-index: 2000 !important;
}
</style>
