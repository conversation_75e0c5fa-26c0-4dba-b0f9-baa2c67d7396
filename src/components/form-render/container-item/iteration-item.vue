<template>
  <div>
    <div v-for="(item, index) in value" :key="index">
      <container-item-wrapper :widget="widget">
        <div :key="widget.id" :class="[customClass]">
          <template v-for="subWidget in widget.widgetList">
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-item'"
                :widget="
                  item[subWidget.id]
                    ? {
                        ...subWidget,
                        options: {
                          ...subWidget.options,
                          ...item[subWidget.id],
                        },
                      }
                    : subWidget
                "
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :subFormRowIndex="index"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="
                  item[subWidget.id]
                    ? {
                        ...subWidget,
                        options: {
                          ...subWidget.options,
                          ...item[subWidget.id],
                        },
                      }
                    : subWidget
                "
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :subFormRowIndex="index"
                :parent-widget="widget"
                :design-state="true"
              ></component>
            </template>
          </template>
        </div>
      </container-item-wrapper>
    </div>
  </div>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper.vue';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'iteration-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    tabIndex: null,
    tablFildId: null,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  data() {
    return {
      value: [],
    };
  },
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  created() {
    this.initRefList();
  },
  mounted() {
    this.handleOnMounted();
    console.log(this.value);
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setValue(items) {
      this.value = items;
    },
    getValue() {
      return this.value;
    },
    setIterationCount(data) {
      this.setValue(data);
    },
  },
};
</script>

<style lang="scss" scoped></style>
