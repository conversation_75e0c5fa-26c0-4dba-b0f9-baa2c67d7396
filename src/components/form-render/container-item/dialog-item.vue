<template>
  <container-item-wrapper :widget="widget">
    <el-dialog
      :title="widget.options.label"
      ref="dialog"
      append-to-body
      :data-isDrag="widget.options.isDrag ? '1' : '0'"
      v-drag-dialog
      :visible.sync="dialogVisible"
      :width="`${widget.options.dialogWidth || 60}%`"
      :before-close="handleClose"
      :close-on-click-modal="widget.options.closeOnClickModal"
      :fullscreen="widget.options.fullscreen"
      :show-close="widget.options.showClose"
      @open="openComplete"
      @close="closeComplete"

    >
      <template slot="title">
        <p
          style="
            font-weight: 600;
            text-align: left;
            font-size: 18px;
            color: #fefefe;
          "
        >
          {{ widget.options.label }}
        </p>
        <el-divider></el-divider>
      </template>
      <div
         v-loading="loading"
        v-if="
          !!widget.widgetList[0].widgetList &&
          widget.widgetList[0].widgetList.length > 0
        "
      >
        <template v-for="(subWidget, swIdx) in widget.widgetList[0].widgetList">
          <template v-if="'container' === subWidget.category">
            <component
              :is="subWidget.type + '-item'"
              :widget="subWidget"
              :key="swIdx"
              :parent-list="widget.widgetList"
              :index-of-parent-list="swIdx"
              :parent-widget="widget"
            ></component>
          </template>
          <template v-else>
            <component
              :is="subWidget.type + '-widget'"
              :field="subWidget"
              :designer="null"
              :key="swIdx"
              :parent-list="widget.widgetList"
              :index-of-parent-list="swIdx"
              :parent-widget="widget"
            ></component>
          </template>
        </template>
      </div>
      <div slot="footer" class="dialog-footer">
        <div
          class="d-flex j-center"
          v-if="!widget.options.isCustom && widget.options.leftBtns.length"
        >
          <div class="d-flex">
            <template v-for="(item, index) in widget.options.leftBtns">
              <el-button
                :key="index"
                :type="item.type"
                :style="{
                  backgroundColor: item.bgColor,
                  borderColor: item.bgColor,
                }"
                :loading="item.loading"
                @click="handleLeftClick(item.fun)"
                v-if="handleLeftDisplay(item.displayFun)"
              >
                <template v-if="item.icon">
                  <i
                    v-if="item.icon && item.icon.includes('el-icon')"
                    :style="{ color: item.color }"
                    :class="item.icon"
                  ></i>
                  <svg-icon
                    :class="[
                      !!!item.color && item.type
                        ? 'el-button--' + item.type
                        : '',
                    ]"
                    v-else
                    :icon-class="item.icon"
                    :style="{ color: item.color, margin: 0 }"
                  />
                </template>
                {{ item.name }}
              </el-button>
            </template>
          </div>
        </div>
        <template v-else>
          <template
            v-if="
              !!widget.widgetList[1].widgetList &&
              widget.widgetList[1].widgetList.length > 0
            "
          >
            <template
              v-for="(subWidget, swIdx) in widget.widgetList[1].widgetList"
            >
              <template v-if="'container' === subWidget.category">
                <component
                  :is="subWidget.type + '-item'"
                  :widget="subWidget"
                  :key="swIdx"
                  :parent-list="widget.widgetList"
                  :index-of-parent-list="swIdx"
                  :parent-widget="widget"
                ></component>
              </template>
              <template v-else>
                <component
                  :is="subWidget.type + '-widget'"
                  :field="subWidget"
                  :designer="null"
                  :key="swIdx"
                  :parent-list="widget.widgetList"
                  :index-of-parent-list="swIdx"
                  :parent-widget="widget"
                ></component>
              </template>
            </template>
          </template>
        </template>
      </div>
    </el-dialog>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper.vue';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'dialog-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
  },
  data() {
    return {
      dialogVisible: false,
      callbackFlag: null,
      timer: '',
      loading:false
    };
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  created() {
    this.initRefList();
    this.dialogVisible = !this.widget.options.hidden;
    if (!this.widget.options.hasOwnProperty('isCustom')) {
      this.$set(this.widget.options, 'isCustom', true);
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setDialogLoading(state) {
      this.loading = state;
    },
    setLoading(index, state) {
      if (this.widget.options.leftBtns && this.widget.options.leftBtns.length) {
        this.$set(this.widget.options.leftBtns[index], 'loading', state);
      }
    },
    toggleCard() {
      this.widget.options.folded = !this.widget.options.folded;
    },
    handleLeftClick(fun) {
      if (fun) {
        let JS = new Function(fun);
        JS.call(this);
      }
    },
    handleLeftDisplay(displayFun) {
      try {
        if (!displayFun) {
          return true;
        } else {
          let cell = new Function(displayFun);
          return cell.call(this);
        }
      } catch (e) {
        return true;
      }
    },
    openComplete() {
      this.callbackFlag = true;
      // 关闭时情况里面字段的校验
      let arr = this.widget.widgetList[0].widgetList;
      let that = this;
      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              try {
                widget.buildFieldRules();
              } catch (e) {
                console.error(widget.field.options.label + '设置错误', e);
              }
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
    },
    closeComplete() {
      this.clearValidate();
      // this.timer=new Date().getTime()
      if (this.widget.options.destroyOnClose) {
        this.clear();
      }

      // 关闭时情况里面字段的校验
      let arr = this.widget.widgetList[0].widgetList;

      this.$array.handleWidgetList(arr, (item) => {
        try {
          let widget = this.getWidgetRef(item.id);
          if (
            widget != null &&
            widget.field &&
            widget.field.category != 'category'
          ) {
            try {
              widget.clearFieldRules();
            } catch (e) {
              console.error(widget.field.options.label + '设置错误', e);
            }
          }
          if (widget._props.widget.type === 'tab') {
            widget.activeTabName = widget.visibleTabs[0].options.label;
          }
        } catch (e) {
          // 里面可能有栅格等字段没有清除接口
        }
      });
    },
    setDialogVisible(flag, callback) {
      this.dialogVisible = flag;
      this.clearValidate();
      // this.timer=new Date().getTime()
      if (!flag && this.widget.options.destroyOnClose) {
        this.clear();
      }

      if (!flag) {
        callback && callback();
        return;
      }
      if (callback) {
        let timer = null;
        try {
          timer = setInterval(() => {
            // console.log('弹窗组件循环')
            if (this.callbackFlag) {
              this.callbackFlag = false;
              clearInterval(timer);
              let row = callback();
              if (row && Object.keys(row).length) this.setCategoryValue(row);
            }
          }, 100);
        } catch (error) {
          clearInterval(timer);
        }
      }

      // callback && callback();
    },
    // setDialogVisibleSync(flag) {
    //   this.dialogVisible = flag;
    //   return new Promise((resolve) => {
    //     // if (this.callbackFlag) {
    //     //   this.callbackFlag = false;
    //     //   resolve('sodjfhi');
    //     // }
    //     let timer = setInterval(() => {
    //       if (this.callbackFlag) {
    //         // callback();
    //         resolve('陈代斌');
    //         this.callbackFlag = false;
    //         clearInterval(timer);
    //       }
    //     }, 0);
    //   });
    // },
    clear() {
      let arr = this.widget.widgetList[0].widgetList;
      let that = this;
      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              that.getWidgetRef(item.id).resetField();
            }

            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.tabs && item.tabs.length > 0) {
              cr(item.tabs);
            }
             if (item.items && item.items.length > 0) {
                cr(item.items);
              }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if (item.type == 'table' && item.rows && item.rows.length > 0) {
              item.rows.forEach((e) => {
                cr(e.cols);
              });
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
    },
    validate() {
      return new Promise((resolve, reject) => {
        let arr = this.widget.widgetList[0].widgetList;
        let that = this;
        let formData = {};
        let cr = (arr) => {
          arr.forEach((item) => {
            try {
              let widget = that.getWidgetRef(item.id);
              if (
                widget != null &&
                widget.field &&
                widget.field.category != 'category'
              ) {
                let errorMessage = widget.validate();

                let validateFlag = errorMessage == '' || !errorMessage;
                // if (widget.field.type == 'tree' && !widget.getValue()) {
                //   validateFlag = false;
                // } else {
                //   validateFlag =
                //     errorMessage == '' || !errorMessage ? true : false;
                // }
                if (widget.field.type == 'tree') {
                  console.error(
                    `树组件${widget.field.id}无法自动校验,需要自行手动校验`,
                  );
                }

                if (!validateFlag) {
                  reject({ field: widget, errorMessage: errorMessage });
                } else {
                  // 如果有别名 设置别名
                  if (widget.field.options.alias) {
                    formData[widget.field.options.alias] = widget.getValue();
                  } else {
                    formData[widget.field.options.name] = widget.getValue();
                  }
                }
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols);
              }
              if (item.tabs && item.tabs.length > 0) {
                cr(item.tabs);
              }
              if (item.items && item.items.length > 0) {
                cr(item.items);
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList);
              }
              if (item.type == 'table' && item.rows && item.rows.length > 0) {
                item.rows.forEach((e) => {
                  cr(e.cols);
                });
              }
            } catch (e) {
              // 里面可能有栅格等字段没有清除接口
              reject({ field: null, errorMessage: '校验错误' });
            }
          });
        };
        //迭代
        cr(arr);
        resolve(formData);
      });
    },
    setDefaultValue(data) {
      if (data && Object.keys(data).length) {
        // 递归遍历
        const dfc = (root) => {
          root.forEach((item) => {
            let alias = item?.options?.alias;
            if (alias && alias in data) {
              this.getWidgetRef(item.id).setValue(data[alias]);
            }
            if ('widgetList' in item && item['widgetList'].length)
              dfc(item['widgetList']);
            if ('cols' in item && item['cols'].length) dfc(item['cols']);
          });
        };
        dfc(this.widget.widgetList);
      } else {
        console.error('setDefaultValue方法接收的参数为一个对象');
      }
    },
    clearValidate() {
      this.getFormRef().$refs.renderForm.clearValidate();
    },
    setCategoryValue(data, flag = true) {
      let arr = this.widget.widgetList[0].widgetList;
      let that = this;
      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              try {
                if (
                  data.hasOwnProperty(widget.field.options.name) ||
                  data.hasOwnProperty(widget.field.options.alias)
                ) {
                  let value =
                    data[widget.field.options.name] ||
                    data[widget.field.options.alias];
                  // 空值也设置
                  if (!value) {
                    if (item.type == 'data-table') {
                      widget.updateTableData([]);
                    } else {
                      widget.resetField();
                    }
                  } else {
                    if (item.type == 'data-table') {
                      if (!value || value !== '') {
                        if (typeof value == 'string') {
                          // 需要转json
                          widget.updateTableData(JSON.parse(value));
                        } else {
                          widget.updateTableData(value);
                        }
                      }
                    } else {
                      if (!value || value !== '') {
                        widget.setValue(value);
                      }
                    }
                  }
                }else if(flag){
                  if (item.type == 'data-table') {
                      widget.updateTableData([]);
                    } else {
                      widget.resetField();
                    }
                }
              } catch (e) {
                console.error(widget.field.options.label + '设置错误', e);
              }
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.tabs && item.tabs.length > 0) {
              cr(item.tabs);
            }
            if (item.items && item.items.length > 0) {
                cr(item.items);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if (item.type == 'table' && item.rows && item.rows.length > 0) {
              item.rows.forEach((e) => {
                cr(e.cols);
              });
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
    },
    handleClose(done) {
      if (this.widget.options.hasCloseTips) {
        this.$confirm(this.widget.options.hasCloseTipsContent)
          .then((_) => {
            this.setDialogVisible(false);
            if (!!this.widget.options.onDialogClose) {
              let customFunc = new Function(this.widget.options.onDialogClose);
              customFunc.call(this);
            }
          })
          .catch((_) => {});
      } else {
        this.setDialogVisible(false);
        if (!!this.widget.options.onDialogClose) {
          let customFunc = new Function(this.widget.options.onDialogClose);
          customFunc.call(this);
        }
      }
    },
    setIsDisable(flag) {
      let list = this.widget.widgetList[0].widgetList;
      let fun = (arr) => {
        arr.forEach((item) => {
          if (item.options.name) {
            this.getWidgetRef(item.options.name).setDisabled &&
              this.getWidgetRef(item.options.name).setDisabled(flag);
          }
          if (item.cols || item.widgetList) {
            fun(item.cols || item.widgetList);
          }
        });
      };
      fun(list);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-card__header {
  padding: 10px 12px;
}

.folded ::v-deep .el-card__body {
  display: none;
}

.clear-fix:before,
.clear-fix:after {
  display: table;
  content: '';
}

.clear-fix:after {
  clear: both;
}

.float-right {
  float: right;
}
::v-deep .el-card {
  overflow: visible !important;
}
::v-deep .el-dialog .el-dialog__body {
  /* max-height: 600px !important; */
  overflow: auto !important;
}
::v-deep .el-divider--horizontal {
  margin: 10px 0;
}
</style>
