import Vue from "vue"

// 使用 Vite 的 import.meta.glob 替代 require.context
const modules = import.meta.glob('./*.vue', { eager: true })

/**
 * 容器组件时递归组件，且内部可以嵌套其他容器，局部注册会找不到组件，必须注册为全局组件，原因不明？！
 * begin
 *
let comps = {}

Object.keys(modules).forEach(fileName => {
  let comp = modules[fileName].default;
  comps[comp.name] = comp
})

export default comps;

end */

/* 全局注册！！ */
Object.keys(modules).forEach(fileName => {
  let comp = modules[fileName].default;
  Vue.component(comp.name, comp)
})
