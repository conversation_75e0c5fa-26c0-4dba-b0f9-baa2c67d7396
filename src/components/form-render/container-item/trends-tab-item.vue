<template>
  <container-item-wrapper :widget="widget">
    <div :key="widget.id" class="tab-container">
      <el-tabs :type="widget.options.tabType" @tab-remove="removeTab">
        <el-tab-pane v-if="!widget.options.readonly">
          <span slot="label">
            <el-button
              type="text"
              style="font-size: 18px"
              icon="el-icon-circle-plus-outline"
              @click="addTab(activeTabs.length)"
            />
          </span>
        </el-tab-pane>

        <el-tab-pane
          v-for="(item, index) in activeTabs"
          :key="index"
          :name="index.toString()"
          :closable="!widget.options.readonly"
        >
          <span slot="label" @dblclick.stop="editTitleIndex = index">
            <span v-if="index === editTitleIndex">
              <el-input
                v-model="item.title"
                size="mini"
                @blur="titleBlur(index)"
              />
            </span>

            <el-button v-else type="text"
              >{{ item.title }}<icon class="el-icon-edit"
            /></el-button>
          </span>
          <template v-for="(tabWidget, swIdx) in widget.widgetList">
            <template v-if="'container' === tabWidget.category">
              <gridItem
                :widget="tabWidget"
                :tab-index="index"
                :tabl-fild-id="1"
                :tab-widget="widget"
              />
            </template>
            <template v-else>
              <component
                :is="tabWidget.type + '-widget'"
                :key="fieldSchemaData[index][swIdx].id"
                :field="fieldSchemaData[index][swIdx]"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
                :sub-form-row-id="index + ''"
                :sub-form-row-index="index"
                :sub-form-col-index="swIdx"
              />
            </template>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </container-item-wrapper>
</template>

<script>
import gridItem from './grid-item.vue'
import i18n from '@/utils/i18n'
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
import emitter from 'element-ui'
import refMixin from '@/components/form-render/refMixin'
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin'
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper.vue'
import { deepClone, generateId } from '@/utils/util'

export default {
  name: 'TrendsTabItem',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    gridItem,
    ContainerItemWrapper,
    ...FieldComponents,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  props: {
    widget: Object,
  },
  data() {
    return {
      tabIndex: 0,
      activeTabs: [],
      fieldSchemaData: [],
      editTitleIndex: -1,
    }
  },
  watch: {},
  created() {
    this.initTabData()
    this.initRefList()
    this.registerSubFormToRefList()
    this.initFieldSchemaData()
  },
  mounted() {},
  beforeDestroy() {
    // this.unregisterFromRefList()
  },
  methods: {
    initTabData() {
      console.log(this.formModel[this.widget.options.name])
      this.activeTabs = this.formModel[this.widget.options.name]
    },
    titleBlur(index) {
      this.editTitleIndex = -1
      this.formModel[this.widget.options.name][index]['title'] =
        this.activeTabs[index].title
    },
    registerSubFormToRefList() {
      this.sfRefList[this.widget.options.name] = this
    },
    initFieldSchemaData() {
      let rowLength = this.activeTabs.length
      this.fieldSchemaData.splice(0, this.fieldSchemaData.length) // 清除数组必须用splice，length=0不会响应式更新！！
      if (rowLength > 0) {
        for (let i = 0; i < rowLength; i++) {
          let fieldSchemas = []
          this.widget.widgetList.forEach((swItem) => {
            fieldSchemas.push(this.cloneFieldSchema(swItem))
          })
          this.fieldSchemaData.push(fieldSchemas)
        }
      }
    },
    cloneFieldSchema(fieldWidget) {
      let newFieldSchema = deepClone(fieldWidget)
      newFieldSchema.id = fieldWidget.type + generateId()
      return newFieldSchema
    },
    addTab() {
      let fieldSchemas = []
      let subFormDataRow = { title: this.widget.options.label }
      this.widget.widgetList.forEach((swItem) => {
        fieldSchemas.push(this.cloneFieldSchema(swItem))
        // subFormDataRow[swItem.options.name] = swItem.options.defaultValue

        if (swItem.category === 'container') {
          swItem.cols.forEach((childFormItem) => {
            childFormItem.widgetList.forEach((childChildFormItem) => {
              subFormDataRow[childChildFormItem.options.name] =
                childChildFormItem.options.defaultValue
            })
          })
        } else {
          subFormDataRow[swItem.options.name] = swItem.options.defaultValue
        }
      })
      this.fieldSchemaData.push(fieldSchemas)

      this.formModel[this.widget.options.name]
        ? this.formModel[this.widget.options.name].push(subFormDataRow)
        : (this.formModel[this.widget.options.name] = [subFormDataRow])

      // this.activeTabs.push({title: 'new tab', tabId: 'tabId' + generateId()})
    },
    removeTab(targetName) {
      this.activeTabs.splice(+targetName, 1)
      this.fieldSchemaData.splice(+targetName, 1)
      // this.formModel[this.widget.options.name].splice(+targetName, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.tab-container {
  //padding: 5px;
  margin: 20px 2px;

  .form-widget-list {
    min-height: 28px;
  }
}
</style>
