<template>
  <container-item-wrapper :widget="widget">
    <el-collapse :key="widget.id"
            :accordion="widget.options.accordion"
            :class="[customClass]"
            :ref="widget.id" v-show="!widget.options.hidden">
      <template v-for="(itemWidget, index) in widget.items">
        <collapse-item-item :widget="itemWidget" :key="index" :title="widget.title" :parent-list="widget.items"
                       :index-of-parent-list="index"
                       :parent-widget="widget"
                       ></collapse-item-item>
      </template>
    </el-collapse>
  </container-item-wrapper>
</template>

<script>
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n from "../../../utils/i18n"
  import refMixin from "../../../components/form-render/refMixin"
  import ContainerItemWrapper from './container-item-wrapper.vue'
  import CollapseItemItem from './collapse-item-item.vue'
  import containerItemMixin from "./containerItemMixin"

  export default {
    name: "collapse-item",
    componentName: 'ContainerItem',
    mixins: [emitter, i18n, refMixin, containerItemMixin],
    components: {
      ContainerItemWrapper,
      CollapseItemItem,
    },
    props: {
      widget: Object,
    },
    inject: ['refList', 'sfRefList', 'globalModel'],
    created() {
      this.initRefList()
    },
    mounted() {

    },
    beforeDestroy() {
      this.unregisterFromRefList()
    },
    methods: {

    },
  }
</script>

<style lang="scss" scoped>

</style>
