<template>
  <div>
    <container-item-wrapper :widget="widget" style="float: left !important;" :style="{
                  height: widget.options.height?( widget.options.height < 1 ? (widget.options.height * 100) + '%': widget.options.height + 'px'):'100%',
                  width: widget.options.width? ( widget.options.width <= 1 ? (widget.options.width * 100) + '%': widget.options.width + 'px'):'100%',
                  float: widget.options.float? widget.options.float:'none',
                  backgroundColor: widget.options.backgroundColor? widget.options.backgroundColor: '#FFFFFF'
                  }">
      <div :key="widget.id"
           :class="[customClass]">
        <template v-for="(subWidget) in widget.widgetList">
          <template v-if="'container' === subWidget.category">
            <component :is="subWidget.type + '-item'" :widget="subWidget"  :key="subWidget.id" :parent-list="widget.widgetList"
                       :parent-widget="widget"></component>
          </template>
          <template v-else>
            <component :is="subWidget.type + '-widget'" :field="subWidget"  :key="subWidget.id" :parent-list="widget.widgetList"
                       :parent-widget="widget" :design-state="true"></component>
          </template>
        </template>
      </div>
    </container-item-wrapper>
  </div>
</template>

<script>
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n from "@/utils/i18n"
  import refMixin from "@/components/form-render/refMixin"
  import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper.vue'
  import containerItemMixin from "@/components/form-render/container-item/containerItemMixin"
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

  export default {
    name: "whiteboard-item",
    componentName: 'ContainerItem',
    mixins: [emitter, i18n, refMixin, containerItemMixin],
    components: {
      ContainerItemWrapper,
      ...FieldComponents,
    },
    props: {
      widget: Object,
      tabIndex: null,
      tablFildId: null
    },
    inject: ['refList', 'sfRefList', 'globalModel'],
    data() {
      return {
        value: []
      }
    },
    computed: {
      customClass() {
        return this.widget.options.customClass || ''
      },

    },
    created() {
      this.initRefList()
    },
    mounted() {
      this.handleOnMounted()
    },
    beforeDestroy() {
      this.unregisterFromRefList()
    },
    methods: {
      setValue(items){
        this.value = items
      },
      getValue(){
        return this.value
      }
    },
  }
</script>

<style lang="scss" scoped>

</style>
