<template>
  <div :key="widget.id" class="pd-b5 d-flex flex-wrap">
    <div v-for="(conditions, i) in screenList" :key="i" class="d-flex">
      <div
        v-for="(item, index) in conditions"
        :key="index"
        class="d-flex a-center pd-b10 pd-r10"
      >
        <!-- 筛选项 -->
        <el-select
          v-model="item.elementKey"
          placeholder="请选择"
          style="width: 200px"
          clearable
          @change="itemChange($event, item)"
        >
          <el-option
            v-for="(item, index) in items"
            :key="index"
            :label="item.options.label"
            :value="item.options.value"
          />
        </el-select>
        <!-- 筛选项目对应的输入框/选择框... -->
        <div class="mr-lr15" style="width: 230px">
          <el-select
            v-if="item.elementType === 'select'"
            v-model="item.value"
            :placeholder="'请选择' + getScreenLabel(item.elementKey)"
            clearable
          >
            <el-option
              v-for="item2 in item.options"
              :key="item2[item.selectValue]||item2.value"
              :label="item2[item.selectLabel]||item2.label"
              :value="item2[item.selectValue]||item2.value"
            />
          </el-select>
          <el-date-picker
            v-else-if="
              item.elementType === 'date-range' ||
              item.elementType === 'time-range'
            "
            v-model="item.value"
            style="width: 100%"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择时间"
            clearable
          />
          <el-date-picker
            v-else-if="
              item.elementType === 'date-time'
            "
            v-model="item.value"
            style="width: 100%"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择时间"
            clearable
          />
          <lt-select-page2
            v-else-if="
              item.elementType === 'select-page' ||
              item.elementType === 'popup-select'
            "
            v-model="item.value"
            :api-id="item.bandApi"
            select-label="label"
            select-value="value"
            search-key="value"
            clearable
            :placeholder="'请选择' + getScreenLabel(item.elementKey)"
          />
          <el-input
            v-else
            v-model="item.value"
            :placeholder="'请输入' + getScreenLabel(item.elementKey)"
            clearable
          />
        </div>
        <!-- 关系 -->
        <el-select
          v-model="item.relation"
          style="width: 80px"
          placeholder="或者/并且"
        >
          <el-option
            v-for="(item, index) in relationList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <i
          v-if="conditions.length > 1 || screenList.length > 1"
          class="el-icon-remove font-20 mr-l10 pointer"
          style="color: #ff4949"
          @click="deleteCondition(i, index)"
        />
        <template
          v-if="i === screenList.length - 1 && index === conditions.length - 1"
        >
          <i
            class="el-icon-circle-plus font-20 mr-l10 pointer"
            style="color: #1890ff"
            @click="addCondition"
          />
        </template>
      </div>
    </div>
    <div>
      <el-button type="primary" class="mr-l10" @click="query">查询</el-button>
      <el-button class="mr-l20" plain @click="reset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { executeInterface } from "@/api/interfaces/interfaces";
import emitter from "element-ui/lib/mixins/emitter";
import i18n from "@/utils/i18n";
import refMixin from "@/components/form-render/refMixin";
import ContainerItemWrapper from "@/components/form-render/container-item/container-item-wrapper.vue";
import containerItemMixin from "@/components/form-render/container-item/containerItemMixin";
import FieldComponents from "@/components/form-designer/form-widget/field-widget/index";
export default {
  name: "condition-item",
  componentName: "ContainerItem",
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    tabIndex: null,
    tablFildId: null,
  },
  inject: ["refList", "sfRefList", "globalModel"],
  created() {
    this.initRefList();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  computed: {
    items() {
      return this.widget.widgetList;
    },
  },
  data() {
    return {
      screenList: [],
      conditions: {
        elementKey: "", // 选中的项目key
        elementType: "", // 选中的项目类型
        value: undefined, // 选中的项目对应输入的值
        relation: "or", // 关系(或/且)
        options: [], // select的选项
        bandApi: "", // 下拉分页的请求
        condition: "",
        elementJson: {},
      },
      relationList: [
        { label: "或者", value: "or" },
        { label: "并且", value: "and" },
      ],
    };
  },
  mounted() {
    this.screenList = [[{ ...this.conditions }]];
  },
  methods: {
    /**
     * 筛选项变化
     * @param {Object} e
     * @param {Object} item
     */
    itemChange(e, item) {
      console.log(item)
      let {
        type: elementType,
        options: { api: bandApi, optionItems: elementSelection,selectLabel,selectValue },
      } = this.items.find((item) => item.options.value === e) || {};
      item.value = undefined;
      item.elementType = elementType;
      item.bandApi = bandApi;
      // 根据选择的类型设置condition的值
      let dictData = {
        select: "eq",
        "select-page": "eq",
        input: "like",
      };
      item.condition = dictData[elementType] || 'eq';
      if (elementType === "select") {
          item.selectLabel = selectLabel;
          item.selectValue = selectValue;
        // 优先取api
        if (bandApi) {
          executeInterface({
            apiId: bandApi,
          }).then((res) => {
            item.options = res.data || [];
          });

          return;
        }
        // 其次取elementSelection
        try {
          item.options = elementSelection;
        } catch (e) {
          item.options = [];
        }
      }
    },
    /**
     * 重置
     */
    reset() {
      this.screenList.forEach((item) => {
        item.forEach((item2) => {
          item2.elementKey = "";
          item2.elementType = "";
          item2.value = undefined;
          item2.relation = "or";
          item2.options = [];
          item2.bandApi = "";
          item2.condition = "";
        });
      });
    },
    /**
     * 查询
     */
    query() {
      if (this.widget.options.onQuery) {
        let JS = new Function("conditions", this.widget.options.onQuery);
        JS.call(this, this.getConditions());
      }
    },
    /**
     * 获取筛选条件
     */
    getConditions() {
      return this.screenList
        .reduce((a, b) => a.concat(b))
        .filter((item) => item.elementKey && item.value)
        .map((item) => {
          return {
            elementKey: item.elementKey,
            value: item.value,
            relation: item.relation,
            condition: item.condition,
          };
        });
    },
    /**
     * 添加条件
     */
    addCondition() {
      if (this.screenList[this.screenList.length - 1].length === 2) {
        this.screenList.push([{ ...this.conditions }]);
      } else {
        this.screenList[this.screenList.length - 1].push({
          ...this.conditions,
        });
      }
      this.$forceUpdate();
    },
    /**
     * 删除条件
     * @param {Object} i
     * @param {Object} index
     */
    deleteCondition(i, index) {
      if (this.screenList[i].length === 2) {
        this.screenList[i].splice(index, 1);
      } else {
        this.screenList.splice(i, 1);
      }

      // 重新排序
      this.conditionChange();
    },
    /**
     * 条件的数据重新排序
     */
    conditionChange() {
      const count = 2;
      let arr = [];
      let arrItem = [];

      this.screenList
        .reduce((a, b) => a.concat(b))
        .forEach((item) => {
          arrItem.push(item);
          if (arrItem.length === count) {
            arr.push(arrItem);
            arrItem = [];
          }
        });

      if (arrItem.length) arr.push(arrItem);

      this.screenList = JSON.parse(JSON.stringify(arr));
    },
    /**
     * 获取筛选项的label
     * @param {Object} value
     */
    getScreenLabel(value) {
      try {
        if (!value) return "相关信息";
        return this.items.find((item) => item.options.value === value).options
          .label;
      } catch (e) {
        return "相关信息";
      }
    },
  },
};
</script>
