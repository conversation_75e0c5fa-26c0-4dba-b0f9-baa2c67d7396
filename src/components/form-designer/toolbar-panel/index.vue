<template>
  <div class="toolbar-container">
    <div class="left-toolbar">
      <el-button
        type="text"
        :disabled="undoDisabled"
        :title="i18nt('designer.toolbar.undoHint')"
        @click="undoHistory"
      >
        <svg-icon icon-class="undo"
      /></el-button>
      <el-button
        type="text"
        :disabled="redoDisabled"
        :title="i18nt('designer.toolbar.redoHint')"
        @click="redoHistory"
      >
        <svg-icon icon-class="redo"
      /></el-button>
      <el-button-group style="margin-left: 20px">
        <el-button
          :type="layoutType === 'PC' ? 'info' : ''"
          @click="changeLayoutType('PC')"
          size="mini"
        >
          {{ i18nt("designer.toolbar.pcLayout") }}
        </el-button>
        <el-button
          :type="layoutType === 'H5' ? 'info' : ''"
          @click="changeLayoutType('H5')"
          size="mini"
        >
          {{ i18nt("designer.toolbar.mobileLayout") }}
        </el-button>
      </el-button-group>
      <el-button
        style="margin-left: 10px"
        size="mini"
        :title="i18nt('designer.toolbar.nodeTreeHint')"
        @click="showNodeTreeDrawer"
      >
        <svg-icon icon-class="node-tree"
      /></el-button>
    </div>
    <!--  组件层次树  -->
    <el-drawer
      :title="i18nt('designer.toolbar.nodeTreeTitle')"
      direction="ltr"
      :visible.sync="showNodeTreeDrawerFlag"
      :modal="false"
      :size="280"
      :destroy-on-close="true"
      class="node-tree-drawer"
    >
      <div class="d-flex a-center">
        <el-input
          placeholder="搜索组件"
          clearable=""
          size="mini"
          v-model="filterText"
        >
        </el-input>
        <i
          :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          @click="changeExpand"
          class="mr-l10"
          style="cursor: pointer; font-size: 16px"
          :title="isExpand ? '收缩' : '展开'"
        ></i>
      </div>

      <el-tree
        ref="nodeTree"
        :data="nodeTreeData"
        node-key="id"
        default-expand-all
        highlight-current
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        class="node-tree mr-t10"
        icon-class="el-icon-arrow-right"
        @node-click="onNodeTreeClick"
      ></el-tree>
    </el-drawer>

    <div class="right-toolbar">
      <el-button type="text" @click="clearFormWidget">
        <i class="el-icon-delete" />
        {{ i18nt("designer.toolbar.clear") }}
      </el-button>
      <el-button type="text" @click="previewForm">
        <i class="el-icon-view" />
        {{ i18nt("designer.toolbar.preview") }}
      </el-button>
      <el-button type="text" @click="previewHtmlForm">
        <i class="el-icon-document" />
        HTML预览
      </el-button>
      <el-button type="text" @click="docVisible = true">文档</el-button>
      <el-button type="text" @click="$refs.backupDialogRef.open(templateId)">
        历史
      </el-button>
      <el-button type="text" @click="oneClickCopy">复制</el-button>
      <el-button type="text" @click="importJson">
        {{ i18nt("designer.toolbar.importJson") }}
      </el-button>
      <el-button type="text" @click="exportJson">
        {{ i18nt("designer.toolbar.exportJson") }}
      </el-button>
      <el-button type="text" @click="generateSave">
        {{ i18nt("designer.toolbar.generateSave") }}
      </el-button>
      <el-button type="text" @click="$router.back(-1)">
        {{ i18nt("designer.toolbar.exit") }}
      </el-button>
    </div>

    <el-dialog
      title="文档"
      width="80%"
      v-drag-dialog
      append-to-body
      @close="docVisible = false"
      :visible.sync="docVisible"
    >
      <el-tabs v-model="activeName">
        <el-tab-pane label="快捷键" name="keyboard">
          <el-descriptions title="常用快捷键" border :column="1">
            <el-descriptions-item
              v-for="item in keyboardList"
              :key="item.label"
              :label="item.label"
            >
              {{ item.value }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-dialog
      :title="i18nt('designer.toolbar.preview')"
      :visible.sync="showPreviewDialogFlag"
      v-if="showPreviewDialogFlag"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
       append-to-body
      v-dialog-drag
      :destroy-on-close="true"
      class="small-padding-dialog"
      width="80%"
      :fullscreen="isfullscreen"
    >
      <div>
        <el-scrollbar class="side-scroll-bar">
          <div
            class="form-render-wrapper"
            :class="[layoutType === 'H5' ? 'h5-layout' : '']"
          >
            <VFormRender
              ref="preForm"
              :form-json="formJson"
              :form-data="testFormData"
              @appendButtonClick="testOnAppendButtonClick"
              @buttonClick="testOnButtonClick"
              @formChange="handleFormChange"
            >
            </VFormRender>
          </div>
        </el-scrollbar>
      </div>

      <code-editor v-model="testFunc" style="display: none"></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="isfullscreen = true"
          v-if="!isfullscreen"
        >
          {{ i18nt("designer.hint.fullscreen") }}
        </el-button>
        <el-button type="primary" @click="isfullscreen = false" v-else>
          {{ i18nt("designer.hint.cancelfullscreen") }}
        </el-button>
        <el-button type="primary" @click="getFormData">
          {{ i18nt("designer.hint.getFormData") }}
        </el-button>
        <el-button type="info" @click="printCompiledHTML">
          打印HTML内容
        </el-button>
        <el-button type="primary" @click="resetForm">
          {{ i18nt("designer.hint.resetForm") }}
        </el-button>
        <el-button type="primary" @click="setFormDisabled">
          {{ i18nt("designer.hint.disableForm") }}
        </el-button>
        <el-button type="primary" @click="setFormEnabled">
          {{ i18nt("designer.hint.enableForm") }}
        </el-button>
        <el-button
          type=""
          @click="(showPreviewDialogFlag = false), (isfullscreen = false)"
        >
          {{ i18nt("designer.hint.closePreview") }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="i18nt('designer.toolbar.importJson')"
      :visible.sync="showImportJsonDialogFlag"
      v-if="showImportJsonDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      center
      v-dialog-drag
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <el-alert
        type="info"
        :title="i18nt('designer.hint.importJsonHint')"
        show-icon
        class="alert-padding"
      ></el-alert>
      <code-editor
        :mode="'json'"
        :readonly="false"
        v-model="importTemplate"
      ></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doJsonImport">
          {{ i18nt("designer.hint.import") }}
        </el-button>
        <el-button @click="showImportJsonDialogFlag = false">
          {{ i18nt("designer.hint.cancel") }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="i18nt('designer.toolbar.exportJson')"
      :visible.sync="showExportJsonDialogFlag"
      v-if="showExportJsonDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      center
      v-dialog-drag
         append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <code-editor
        :mode="'json'"
        :readonly="true"
        v-model="jsonContent"
      ></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          class="copy-json-btn"
          :data-clipboard-text="jsonRawContent"
          @click="copyFormJson"
        >
          {{ i18nt("designer.hint.copyJson") }}
        </el-button>
        <el-button @click="saveFormJson">
          {{ i18nt("designer.hint.saveFormJson") }}
        </el-button>
        <el-button type="" @click="showExportJsonDialogFlag = false">
          {{ i18nt("designer.hint.closePreview") }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="i18nt('designer.hint.exportFormData')"
      :visible.sync="showFormDataDialogFlag"
      v-if="showFormDataDialogFlag"
      :show-close="true"
      class="dialog-title-light-bg"
      center
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :append-to-body="true"
    >
      <div style="border: 1px solid #dcdfe6">
        <code-editor
          :mode="'json'"
          :readonly="true"
          v-model="formDataJson"
        ></code-editor>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          class="copy-form-data-json-btn"
          :data-clipboard-text="formDataRawJson"
          @click="copyFormDataJson"
        >
          {{ i18nt("designer.hint.copyFormData") }}
        </el-button>
        <el-button @click="saveFormData">
          {{ i18nt("designer.hint.saveFormData") }}
        </el-button>
        <el-button type="" @click="showFormDataDialogFlag = false">
          {{ i18nt("designer.hint.closePreview") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- HTML预览对话框 -->
    <el-dialog
      title="HTML预览"
      :visible.sync="showHtmlPreviewDialogFlag"
      v-if="showHtmlPreviewDialogFlag"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
      append-to-body
      v-dialog-drag
      :destroy-on-close="true"
      class="html-preview-dialog"
      width="90%"
      :fullscreen="htmlPreviewFullscreen"
    >
      <div class="html-preview-container">
        <div class="preview-toolbar">
          <el-button-group>
            <el-button
              size="mini"
              :type="htmlPreviewMode === 'preview' ? 'primary' : ''"
              @click="htmlPreviewMode = 'preview'"
            >
              <i class="el-icon-view"></i> 预览效果
            </el-button>
            <el-button
              size="mini"
              :type="htmlPreviewMode === 'code' ? 'primary' : ''"
              @click="htmlPreviewMode = 'code'"
            >
              <i class="el-icon-document"></i> HTML代码
            </el-button>
            <el-button
              size="mini"
              :type="htmlPreviewMode === 'split' ? 'primary' : ''"
              @click="htmlPreviewMode = 'split'"
            >
              <i class="el-icon-s-grid"></i> 分屏显示
            </el-button>
          </el-button-group>

          <el-button-group style="margin-left: 10px;">
            <el-button size="mini" @click="refreshHtmlPreview">
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
            <el-button size="mini" @click="copyHtmlCode">
              <i class="el-icon-document-copy"></i> 复制代码
            </el-button>
            <el-button size="mini" @click="downloadHtmlFile">
              <i class="el-icon-download"></i> 下载HTML
            </el-button>
          </el-button-group>
        </div>

        <!-- 预览模式 -->
        <div v-if="htmlPreviewMode === 'preview'" class="preview-only">
          <iframe
            ref="htmlPreviewFrame"
            :srcdoc="generatedHtmlContent"
            class="html-preview-frame"
            frameborder="0"
          ></iframe>
        </div>

        <!-- 代码模式 -->
        <div v-if="htmlPreviewMode === 'code'" class="code-only">
          <code-editor
            :mode="'html'"
            :readonly="true"
            v-model="generatedHtmlContent"
            style="height: 500px;"
          ></code-editor>
        </div>

        <!-- 分屏模式 -->
        <div v-if="htmlPreviewMode === 'split'" class="split-view">
          <div class="split-left">
            <h4>HTML代码</h4>
            <code-editor
              :mode="'html'"
              :readonly="true"
              v-model="generatedHtmlContent"
              style="height: 450px;"
            ></code-editor>
          </div>
          <div class="split-right">
            <h4>预览效果</h4>
            <iframe
              :srcdoc="generatedHtmlContent"
              class="html-preview-frame"
              frameborder="0"
              style="height: 450px;"
            ></iframe>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="htmlPreviewFullscreen = true"
          v-if="!htmlPreviewFullscreen"
        >
          <i class="el-icon-full-screen"></i> 全屏
        </el-button>
        <el-button
          type="primary"
          @click="htmlPreviewFullscreen = false"
          v-else
        >
          <i class="el-icon-aim"></i> 退出全屏
        </el-button>
        <el-button @click="showHtmlPreviewDialogFlag = false">
          关闭
        </el-button>
      </div>
    </el-dialog>

    <backupDialog ref="backupDialogRef" @doJsonImport="doJsonImportBack" />
  </div>
</template>

<script>
import VFormRender from "@/components/form-render/index.vue";
import CodeEditor from "@/components/code-editor/index.vue";
import Clipboard from "clipboard";
import {
  deepClone,
  copyToClipboard,
  generateId,
  getQueryParam,
  traverseAllWidgets,
} from "@/utils/util";
import i18n from "@/utils/i18n";
import { generateCode } from "@/utils/code-generator";
import { genSFC } from "@/utils/sfc-generator";
import loadBeautifier from "@/utils/beautifierLoader";
import { saveAs } from "file-saver";
import { updateTemplateInfoJson } from "@/api/tool/form";
// import bus from '@/scripts/bus';
import bus from "@/scripts/bus";
import { saveBackupData } from "@/api/system/log";
import backupDialog from "./backupDialog.vue";
export default {
  name: "ToolbarPanel",
  mixins: [i18n],
  components: {
    VFormRender,
    CodeEditor,
    Clipboard,
    backupDialog,
  },
  props: {
    designer: Object,
    templateId: "",
  },
  inject: ["formObj"],
  data() {
    return {
      showPreviewDialogFlag: false,
      showHtmlPreviewDialogFlag: false,
      showImportJsonDialogFlag: false,
      showExportJsonDialogFlag: false,
      showExportCodeDialogFlag: false,
      showFormDataDialogFlag: false,
      showExportSFCDialogFlag: false,
      isfullscreen: false,
      testFunc: "",
      importTemplate: "",
      jsonContent: "",
      jsonRawContent: "",

      formDataJson: "",
      formDataRawJson: "",

      vueCode: "",
      htmlCode: "",
      sfcCode: "",
      sfcCodeV3: "",
      filterText: "",
      activeCodeTab: "vue",
      activeSFCTab: "vue2",

      testFormData: {
        // 'userName': '666888',
        // 'productItems': [
        //   {'pName': 'iPhone12', 'pNum': 10},
        //   {'pName': 'P50', 'pNum': 16},
        // ]
      },
      nodeTreeData: [],
      showNodeTreeDrawerFlag: false,
      timer: null,
      isExpand: true,
      docVisible: false,
      activeName: "keyboard",
      keyboardList: [
        {label:'Ctrl+s',value:'保存当前视图'},
        {label:'Ctrl+c',value:'复制组件'},
        {label:'Ctrl+v',value:'粘贴组件'},
        {label:'Ctrl+z',value:'撤回一步操作'},
        {label:'Ctrl+y',value:'前进一步操作'},
        {label:'Ctrl+↑',value:'选中父节点'},
        {label:'Ctrl+↓',value:'选中第一个子节点'},
        {label:'Ctrl+←',value:'选中上一个节点'},
        {label:'Ctrl+→',value:'选中下一个节点'},
        {label:'Delete',value:'删除当前选中节点'},
      ],

      // HTML预览相关
      htmlPreviewMode: 'preview', // preview, code, split
      htmlPreviewFullscreen: false,
      generatedHtmlContent: '',
    };
  },
  computed: {
    formJson() {
      return {
        widgetList: JSON.parse(JSON.stringify(this.designer.widgetList)),
        formConfig: JSON.parse(JSON.stringify(this.designer.formConfig)),
      };
    },

    undoDisabled() {
      return !this.designer.undoEnabled();
    },

    redoDisabled() {
      return !this.designer.redoEnabled();
    },

    layoutType() {
      return this.designer.getLayoutType();
    },
  },
  mounted() {
    document.addEventListener("keydown", (e)=>{
      if(e.ctrlKey && e.keyCode == 90){
         this.undoHistory()
      }else if(e.ctrlKey && e.keyCode == 89){
         this.redoHistory()
      }
    });
    this.scrollerHeight = window.innerHeight - 200 + "px";
    // 点击从预览切换编辑时候触发的事件
    bus.$on("switch-view-status", () => {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      bus.$emit(
        "view-change",
        JSON.stringify({ widgetList, formConfig }, null, "  "),
        false
      );
    });
    // magic头部触发事件
    bus.$on("view-import", this.importJson);
    bus.$on("view-export", this.exportJson);
    bus.$on("view-all-clear", this.clearFormWidget);
    if (localStorage.getItem("templateType") === "3") {
      this.designer.changeLayoutType("H5");
    }
    if (!bus._events["shortcutKeys"]) {
      bus.$on("shortcutKeys", (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === "s") {
          //  执行save方法
          this.generateSave();
          e.preventDefault();
        }
      });
    }
  },
  beforeDestroy(){
    document.removeEventListener("keydown")
    clearInterval(this.timer);
  },
  watch: {
    filterText(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  methods: {
    changeExpand() {
      this.isExpand = !this.isExpand;
      for (
        var i = 0;
        i < this.$refs.nodeTree.store._getAllNodes().length;
        i++
      ) {
        // 根据isExpand， tree展开或折叠
        this.$refs.nodeTree.store._getAllNodes()[i].expanded = this.isExpand;
      }
    },

    filterNode(value, data, node) {
      if (!value) {
        return true;
      }
      let level = node.level;
      let _array = []; //这里使用数组存储 只是为了存储值。
      this.getReturnNode(node, _array, value);
      let result = false;
      _array.forEach((item) => {
        result = result || item;
      });
      return result;
    },
    getReturnNode(node, _array, value) {
      let isPass =
        node.data && node.data.label && node.data.label.indexOf(value) !== -1;
      isPass ? _array.push(isPass) : "";
      if (!isPass && node.level != 1 && node.parent) {
        this.getReturnNode(node.parent, _array, value);
      }
    },

    oneClickCopy(e) {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      let data = { widgetList, formConfig };

      copyToClipboard(
        this.$string.dataTemplateReplace(data),
        e,
        this.$message,
        this.i18nt("designer.hint.copyJsonSuccess"),
        this.i18nt("designer.hint.copyJsonFail")
      );
    },
    buildTreeNodeOfWidget(widget, treeNode) {
      let curNode = {
        id: widget.id,
        label: widget.options.label || widget.options.title || widget.id,
        //selectable: true,
      };
      treeNode.push(curNode);

      if (widget.category === undefined) {
        return;
      }

      curNode.children = [];
      if (widget.type === "grid") {
        widget.cols.map((col) => {
          let colNode = {
            id: col.id,
            label: col.options.label || widget.id,
            children: [],
          };
          curNode.children.push(colNode);
          col.widgetList &&
            col.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, colNode.children);
            });
        });
      } else if (widget.type === "table") {
        //TODO: 需要考虑合并单元格！！
        widget.rows.map((row) => {
          let rowNode = {
            id: row.id,
            label: "table-row",
            selectable: false,
            children: [],
          };
          curNode.children.push(rowNode);

          row.cols.map((cell) => {
            if (!!cell.merged) {
              //跳过合并单元格！！
              return;
            }

            let rowChildren = rowNode.children;
            let cellNode = {
              id: cell.id,
              label: "table-cell",
              children: [],
            };
            rowChildren.push(cellNode);

            cell.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === "tab") {
        widget.tabs.map((tab) => {
          let tabNode = {
            id: tab.id,
            label: tab.options.name || widget.type,
            selectable: false,
            children: [],
          };
          curNode.children.push(tabNode);
          tab.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === "sub-form") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "trends-tab") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "collapse") {
        widget.items.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.category === "container") {
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },
    findWidgetById(wId) {
      let foundW = null;
      traverseAllWidgets(this.designer.widgetList, (w) => {
        if (w.id === wId) {
          foundW = w;
        }
      });

      return foundW;
    },
    onNodeTreeClick(nodeData, node, nodeEl) {
      //console.log('test', JSON.stringify(nodeData))

      if (nodeData.selectable !== undefined && !nodeData.selectable) {
        this.$message.info(
          this.i18nt("designer.hint.currentNodeCannotBeSelected")
        );
      } else {
        const selectedId = nodeData.id;
        const foundW = this.findWidgetById(selectedId);
        if (!!foundW) {
          this.designer.setSelected(foundW);
        }
      }
    },
    refreshNodeTree() {
      this.nodeTreeData.length = 0;
      this.designer.widgetList.forEach((wItem) => {
        this.buildTreeNodeOfWidget(wItem, this.nodeTreeData);
      });
    },
    showNodeTreeDrawer() {
      this.refreshNodeTree();
      this.showNodeTreeDrawerFlag = true;
      this.$nextTick(() => {
        if (!!this.designer.selectedId) {
          //同步当前选中组件到节点树！！！
          this.$refs.nodeTree.setCurrentKey(this.designer.selectedId);
        }
      });
    },
    undoHistory() {
      this.designer.undoHistoryStep();
    },

    redoHistory() {
      this.designer.redoHistoryStep();
    },

    changeLayoutType(newType) {
      this.designer.changeLayoutType(newType);
    },

    clearFormWidget() {
      this.designer.clearDesigner();
    },

    previewForm() {
      this.showPreviewDialogFlag = true;
      // 等待DOM更新后获取编译后的HTML内容
      this.$nextTick(() => {
        setTimeout(() => {
          this.printCompiledHTML();
        }, 500); // 延迟500ms确保表单完全渲染
      });
    },

    // HTML预览
    previewHtmlForm() {
      this.generateHtmlContent();
      this.showHtmlPreviewDialogFlag = true;
    },

    // 生成HTML内容
    generateHtmlContent() {
      try {
        const formConfig = this.formJson.formConfig || {};
        const widgetList = this.formJson.widgetList || [];

        // 生成表单HTML结构
        const formHtml = this.generateFormHtml(widgetList);

        // 生成完整的HTML页面
        this.generatedHtmlContent = this.buildStandaloneHtml(formHtml, formConfig);

        console.log('生成的HTML内容:', this.generatedHtmlContent);
      } catch (error) {
        console.error('生成HTML内容失败:', error);
        this.$message.error('生成HTML内容失败: ' + error.message);
      }
    },

    // 生成表单HTML结构
    generateFormHtml(widgetList) {
      let html = '<form class="el-form render-form" style="padding: 20px;">\n';

      widgetList.forEach(widget => {
        html += this.generateWidgetHtml(widget);
      });

      html += '</form>\n';
      return html;
    },

    // 生成单个组件的HTML
    generateWidgetHtml(widget) {
      const options = widget.options || {};
      const label = options.label || widget.id;
      const name = options.name || widget.id;
      const placeholder = options.placeholder || '';
      const required = options.required ? 'required' : '';

      let html = `  <div class="el-form-item" style="margin-bottom: 22px;">\n`;
      html += `    <label class="el-form-item__label" style="width: 80px; text-align: right; padding-right: 12px;">${label}:</label>\n`;
      html += `    <div class="el-form-item__content" style="margin-left: 80px;">\n`;

      switch (widget.type) {
        case 'input':
          html += `      <input type="text" name="${name}" placeholder="${placeholder}" ${required} class="el-input__inner" style="width: 100%; height: 40px; padding: 0 15px; border: 1px solid #dcdfe6; border-radius: 4px;">\n`;
          break;

        case 'textarea':
          const rows = options.rows || 3;
          html += `      <textarea name="${name}" placeholder="${placeholder}" ${required} rows="${rows}" class="el-textarea__inner" style="width: 100%; padding: 5px 15px; border: 1px solid #dcdfe6; border-radius: 4px; resize: vertical;">${options.defaultValue || ''}</textarea>\n`;
          break;

        case 'number':
          html += `      <input type="number" name="${name}" placeholder="${placeholder}" ${required} class="el-input__inner" style="width: 100%; height: 40px; padding: 0 15px; border: 1px solid #dcdfe6; border-radius: 4px;">\n`;
          break;

        case 'select':
          html += `      <select name="${name}" ${required} class="el-select" style="width: 100%;">\n`;
          html += `        <option value="">请选择${label}</option>\n`;
          if (options.optionItems && Array.isArray(options.optionItems)) {
            options.optionItems.forEach(item => {
              html += `        <option value="${item.value}">${item.label}</option>\n`;
            });
          }
          html += `      </select>\n`;
          break;

        case 'radio':
          if (options.optionItems && Array.isArray(options.optionItems)) {
            options.optionItems.forEach((item, index) => {
              html += `      <label style="margin-right: 15px;"><input type="radio" name="${name}" value="${item.value}" ${required}> ${item.label}</label>\n`;
            });
          }
          break;

        case 'checkbox':
          if (options.optionItems && Array.isArray(options.optionItems)) {
            options.optionItems.forEach((item, index) => {
              html += `      <label style="margin-right: 15px;"><input type="checkbox" name="${name}[]" value="${item.value}"> ${item.label}</label>\n`;
            });
          }
          break;

        case 'date':
          html += `      <input type="date" name="${name}" ${required} class="el-input__inner" style="width: 100%; height: 40px; padding: 0 15px; border: 1px solid #dcdfe6; border-radius: 4px;">\n`;
          break;

        case 'time':
          html += `      <input type="time" name="${name}" ${required} class="el-input__inner" style="width: 100%; height: 40px; padding: 0 15px; border: 1px solid #dcdfe6; border-radius: 4px;">\n`;
          break;

        case 'switch':
          html += `      <label class="el-switch"><input type="checkbox" name="${name}"><span class="el-switch__core"></span></label>\n`;
          break;

        case 'button':
          const buttonType = options.type || 'primary';
          html += `      <button type="button" class="el-button el-button--${buttonType}" style="padding: 12px 20px; border-radius: 4px; border: none; cursor: pointer;">${label}</button>\n`;
          break;

        case 'picture-upload':
          html += `      <div class="upload-area" style="border: 2px dashed #d9d9d9; border-radius: 6px; padding: 20px; text-align: center;">\n`;
          html += `        <input type="file" name="${name}" accept="image/*" style="display: none;" id="${name}_file">\n`;
          html += `        <label for="${name}_file" style="cursor: pointer; color: #409eff;">点击上传图片</label>\n`;
          html += `      </div>\n`;
          break;

        case 'file-upload':
          html += `      <div class="upload-area" style="border: 2px dashed #d9d9d9; border-radius: 6px; padding: 20px; text-align: center;">\n`;
          html += `        <input type="file" name="${name}" style="display: none;" id="${name}_file">\n`;
          html += `        <label for="${name}_file" style="cursor: pointer; color: #409eff;">点击上传文件</label>\n`;
          html += `      </div>\n`;
          break;

        default:
          html += `      <div style="padding: 10px; background: #f5f5f5; border-radius: 4px;">未支持的组件类型: ${widget.type}</div>\n`;
      }

      html += `    </div>\n`;
      html += `  </div>\n`;

      return html;
    },

    // 构建独立的HTML页面
    buildStandaloneHtml(formHtml, formConfig) {
      const cssCode = formConfig.cssCode || '';
      const functions = formConfig.functions || '';
      const formData = this.testFormData || {};

      return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单HTML预览</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .el-form {
            margin: 0;
        }
        .el-form-item {
            margin-bottom: 22px;
            display: flex;
            align-items: flex-start;
        }
        .el-form-item__label {
            width: 80px;
            text-align: right;
            padding-right: 12px;
            line-height: 40px;
            color: #606266;
            font-size: 14px;
        }
        .el-form-item__content {
            flex: 1;
            margin-left: 0;
        }
        .el-input__inner, .el-textarea__inner {
            width: 100%;
            height: 40px;
            padding: 0 15px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            color: #606266;
            background-color: #fff;
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
        .el-input__inner:focus, .el-textarea__inner:focus {
            outline: none;
            border-color: #409eff;
        }
        .el-textarea__inner {
            height: auto;
            padding: 5px 15px;
            resize: vertical;
        }
        .el-select {
            width: 100%;
            height: 40px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            color: #606266;
            background-color: #fff;
        }
        .el-button {
            padding: 12px 20px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .el-button--primary {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
        }
        .el-button--primary:hover {
            background-color: #66b1ff;
            border-color: #66b1ff;
        }
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #409eff;
        }
        /* 自定义CSS */
        ${cssCode}
    </style>
</head>
<body>
    <div class="container">
        <h2 style="margin-top: 0; color: #303133;">表单预览</h2>
        ${formHtml}
        <div style="margin-top: 30px; text-align: center;">
            <button type="button" class="el-button el-button--primary" onclick="submitForm()">提交表单</button>
            <button type="button" class="el-button" onclick="resetForm()" style="margin-left: 10px;">重置表单</button>
        </div>
    </div>

    <script>
        // 全局函数
        ${functions}

        // 表单数据
        const formData = ${JSON.stringify(formData, null, 2)};

        // 表单提交
        function submitForm() {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const data = {};

            for (let [key, value] of formData.entries()) {
                if (data[key]) {
                    if (Array.isArray(data[key])) {
                        data[key].push(value);
                    } else {
                        data[key] = [data[key], value];
                    }
                } else {
                    data[key] = value;
                }
            }

            console.log('表单数据:', data);
            alert('表单提交成功！请查看控制台输出。');
        }

        // 重置表单
        function resetForm() {
            document.querySelector('form').reset();
        }

        // 表单事件处理
        ${formConfig.onFormMounted ?
        `// 表单挂载事件
        document.addEventListener('DOMContentLoaded', function() {
            ${formConfig.onFormMounted}
        });` : ''}

        ${formConfig.onFormDataChange ?
        `// 表单数据变化事件
        document.addEventListener('change', function(e) {
            const fieldName = e.target.name;
            const newValue = e.target.value;
            ${formConfig.onFormDataChange}
        });` : ''}
    <\/script>
</body>
</html>`;
    },

    // 刷新HTML预览
    refreshHtmlPreview() {
      this.generateHtmlContent();
      this.$message.success('HTML预览已刷新');
    },

    // 复制HTML代码
    copyHtmlCode() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.generatedHtmlContent).then(() => {
          this.$message.success('HTML代码已复制到剪贴板');
        }).catch(err => {
          console.error('复制失败:', err);
          this.$message.error('复制失败');
        });
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = this.generatedHtmlContent;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand('copy');
          this.$message.success('HTML代码已复制到剪贴板');
        } catch (err) {
          this.$message.error('复制失败');
        }
        document.body.removeChild(textArea);
      }
    },

    // 下载HTML文件
    downloadHtmlFile() {
      try {
        const blob = new Blob([this.generatedHtmlContent], { type: 'text/html;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `form-preview-${new Date().getTime()}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        this.$message.success('HTML文件下载成功');
      } catch (error) {
        console.error('下载失败:', error);
        this.$message.error('下载失败');
      }
    },

    // 打印编译后的原始HTML内容
    printCompiledHTML() {
      try {
        if (this.$refs.preForm) {
          // 获取VFormRender组件的原生form引用
          const nativeForm = this.$refs.preForm.getNativeForm();
          if (nativeForm && nativeForm.$el) {
            // 获取完整的HTML内容
            const htmlContent = nativeForm.$el.outerHTML;

            console.group('📋 表单预览 - 完整编译信息');

            // 1. HTML结构
            console.group('🔍 HTML结构');
            console.log('原始HTML:', htmlContent);
            console.log('格式化HTML:', this.formatHTML(htmlContent));
            console.groupEnd();

            // 2. JavaScript事件和方法
            console.group('⚡ JavaScript事件和方法');
            this.printJavaScriptEvents();
            console.groupEnd();

            // 3. 接口请求配置
            console.group('🌐 接口请求配置');
            this.printApiConfigurations();
            console.groupEnd();

            // 4. 表单配置和数据
            console.group('⚙️ 表单配置和数据');
            console.log('表单JSON配置:', this.formJson);
            console.log('表单数据模型:', this.$refs.preForm.formDataModel);
            console.log('组件引用列表:', this.$refs.preForm.widgetRefList);
            console.groupEnd();

            // 5. Vue组件实例信息
            console.group('🔧 Vue组件实例信息');
            this.printVueInstanceInfo();
            console.groupEnd();

            // 6. 生成完整的可执行代码
            console.group('📄 完整可执行代码');
            this.generateExecutableCode(htmlContent);
            console.groupEnd();

            console.groupEnd();

            // 将完整信息复制到剪贴板
            const completeInfo = this.generateCompleteInfo(htmlContent);
            if (navigator.clipboard) {
              navigator.clipboard.writeText(completeInfo).then(() => {
                console.log('✅ 完整信息已复制到剪贴板');
              }).catch(err => {
                console.warn('⚠️ 复制到剪贴板失败:', err);
              });
            }
          } else {
            console.warn('⚠️ 无法获取表单DOM元素');
          }
        } else {
          console.warn('⚠️ 预览表单组件引用不存在');
        }
      } catch (error) {
        console.error('❌ 获取HTML内容时发生错误:', error);
      }
    },

    // 打印JavaScript事件和方法
    printJavaScriptEvents() {
      try {
        const events = {};

        // 1. 表单级别事件
        if (this.formJson.formConfig) {
          const formConfig = this.formJson.formConfig;
          if (formConfig.onFormCreated) {
            events.onFormCreated = formConfig.onFormCreated;
          }
          if (formConfig.onFormMounted) {
            events.onFormMounted = formConfig.onFormMounted;
          }
          if (formConfig.onFormDataChange) {
            events.onFormDataChange = formConfig.onFormDataChange;
          }
          if (formConfig.onFormCommit) {
            events.onFormCommit = formConfig.onFormCommit;
          }
          if (formConfig.functions) {
            events.globalFunctions = formConfig.functions;
          }
        }

        // 2. 组件级别事件
        const widgetEvents = {};
        this.traverseWidgets(this.formJson.widgetList, (widget) => {
          if (widget.options) {
            const widgetEventInfo = {};
            if (widget.options.onChange) {
              widgetEventInfo.onChange = widget.options.onChange;
            }
            if (widget.options.onClick) {
              widgetEventInfo.onClick = widget.options.onClick;
            }
            if (widget.options.onRemoteQuery) {
              widgetEventInfo.onRemoteQuery = widget.options.onRemoteQuery;
            }
            if (widget.options.onValidate) {
              widgetEventInfo.onValidate = widget.options.onValidate;
            }
            if (Object.keys(widgetEventInfo).length > 0) {
              widgetEvents[widget.id] = {
                type: widget.type,
                name: widget.options.name,
                label: widget.options.label,
                events: widgetEventInfo
              };
            }
          }
        });

        console.log('表单级别事件:', events);
        console.log('组件级别事件:', widgetEvents);

        // 3. Vue实例事件监听器
        if (this.$refs.preForm && this.$refs.preForm._events) {
          console.log('Vue事件监听器:', this.$refs.preForm._events);
        }

      } catch (error) {
        console.error('获取JavaScript事件失败:', error);
      }
    },

    // 打印API配置信息
    printApiConfigurations() {
      try {
        const apiConfigs = {};

        // 遍历所有组件，查找API配置
        this.traverseWidgets(this.formJson.widgetList, (widget) => {
          if (widget.options) {
            const apiInfo = {};

            // 选项数据源API
            if (widget.options.optionItems && Array.isArray(widget.options.optionItems)) {
              widget.options.optionItems.forEach((item, index) => {
                if (item.loadFromApi) {
                  apiInfo[`optionApi_${index}`] = {
                    type: 'option',
                    url: item.apiUrl,
                    method: item.apiMethod || 'GET',
                    params: item.apiParams
                  };
                }
              });
            }

            // 远程查询API
            if (widget.options.remote && widget.options.remoteUrl) {
              apiInfo.remoteQuery = {
                type: 'remote',
                url: widget.options.remoteUrl,
                method: widget.options.remoteMethod || 'GET'
              };
            }

            // 文件上传API
            if (widget.type === 'picture-upload' || widget.type === 'file-upload') {
              if (widget.options.uploadURL) {
                apiInfo.upload = {
                  type: 'upload',
                  url: widget.options.uploadURL,
                  method: 'POST'
                };
              }
            }

            // 子表单API
            if (widget.type === 'sub-form' && widget.options.api) {
              apiInfo.subFormApi = {
                type: 'subform',
                apiId: widget.options.api
              };
            }

            if (Object.keys(apiInfo).length > 0) {
              apiConfigs[widget.id] = {
                type: widget.type,
                name: widget.options.name,
                label: widget.options.label,
                apis: apiInfo
              };
            }
          }
        });

        console.log('组件API配置:', apiConfigs);

        // 全局API配置
        if (this.$refs.preForm && this.$refs.preForm.waitHttp) {
          console.log('全局API方法:', 'waitHttp - 用于执行魔法接口');
        }

      } catch (error) {
        console.error('获取API配置失败:', error);
      }
    },

    // 打印Vue实例信息
    printVueInstanceInfo() {
      try {
        if (this.$refs.preForm) {
          const vueInfo = {
            componentName: this.$refs.preForm.$options.name,
            mixins: this.$refs.preForm.$options.mixins?.map(m => m.name || 'anonymous'),
            computed: Object.keys(this.$refs.preForm.$options.computed || {}),
            methods: Object.keys(this.$refs.preForm.$options.methods || {}),
            watchers: Object.keys(this.$refs.preForm._watchers || {}),
            refs: Object.keys(this.$refs.preForm.$refs || {}),
            listeners: Object.keys(this.$refs.preForm.$listeners || {})
          };

          console.log('Vue组件信息:', vueInfo);
          console.log('组件实例数据:', {
            formDataModel: this.$refs.preForm.formDataModel,
            widgetRefList: this.$refs.preForm.widgetRefList,
            subFormRefList: this.$refs.preForm.subFormRefList
          });
        }
      } catch (error) {
        console.error('获取Vue实例信息失败:', error);
      }
    },

    // 遍历所有组件的工具方法
    traverseWidgets(widgetList, callback) {
      if (!widgetList || !Array.isArray(widgetList)) return;

      widgetList.forEach(widget => {
        callback(widget);

        // 递归处理容器组件
        if (widget.category === 'container') {
          if (widget.widgetList) {
            this.traverseWidgets(widget.widgetList, callback);
          }
          if (widget.type === 'grid' && widget.cols) {
            widget.cols.forEach(col => {
              if (col.widgetList) {
                this.traverseWidgets(col.widgetList, callback);
              }
            });
          }
          if (widget.type === 'table' && widget.rows) {
            widget.rows.forEach(row => {
              if (row.cols) {
                row.cols.forEach(cell => {
                  if (cell.widgetList) {
                    this.traverseWidgets(cell.widgetList, callback);
                  }
                });
              }
            });
          }
          if (widget.type === 'tab' && widget.tabs) {
            widget.tabs.forEach(tab => {
              if (tab.widgetList) {
                this.traverseWidgets(tab.widgetList, callback);
              }
            });
          }
        }
      });
    },

    // 格式化HTML内容，便于阅读
    formatHTML(html) {
      try {
        // 简单的HTML格式化
        return html
          .replace(/></g, '>\n<')
          .replace(/^\s*\n/gm, '')
          .split('\n')
          .map((line, index) => {
            const indent = '  '.repeat(Math.max(0, (line.match(/</g) || []).length - (line.match(/\//g) || []).length));
            return `${index + 1}: ${indent}${line.trim()}`;
          })
          .join('\n');
      } catch (error) {
        console.warn('HTML格式化失败:', error);
        return html;
      }
    },

    // 生成完整的可执行代码
    generateExecutableCode(htmlContent) {
      try {
        const completeCode = this.buildCompleteHTML(htmlContent);
        console.log('完整HTML页面:', completeCode);

        const vueCode = this.buildVueComponent();
        console.log('Vue组件代码:', vueCode);

        const jsCode = this.buildJavaScriptCode();
        console.log('JavaScript代码:', jsCode);

      } catch (error) {
        console.error('生成可执行代码失败:', error);
      }
    },

    saveAsFile(fileContent, defaultFileName) {
      this.$prompt(
        this.i18nt("designer.hint.fileNameForSave"),
        this.i18nt("designer.hint.saveFileTitle"),
        {
          inputValue: defaultFileName,
          closeOnClickModal: false,
          inputPlaceholder: this.i18nt(
            "designer.hint.fileNameInputPlaceholder"
          ),
        }
      )
        .then(({ value }) => {
          if (!value) {
            value = defaultFileName;
          }

          if (getQueryParam("vscode") == 1) {
            this.vsSaveFile(value, fileContent);
            return;
          }

          const fileBlob = new Blob([fileContent], {
            type: "text/plain;charset=utf-8",
          });
          saveAs(fileBlob, value);
        })
        .catch(() => {
          //
        });
    },

    vsSaveFile(fileName, fileContent) {
      const msgObj = {
        cmd: "writeFile",
        data: {
          fileName,
          code: fileContent,
        },
      };
      window.parent.postMessage(msgObj, "*");
    },

    importJson() {
      this.importTemplate = JSON.stringify(
        this.designer.getImportTemplate(),
        null,
        "  "
      );
      this.showImportJsonDialogFlag = true;
    },

    doJsonImport() {
      try {
        let importObj = JSON.parse(this.importTemplate);
        this.designer.loadFormJson(importObj);

        this.showImportJsonDialogFlag = false;
        this.$message.success(this.i18nt("designer.hint.importJsonSuccess"));

        this.designer.emitHistoryChange();

        this.designer.emitEvent("form-json-imported", []);
      } catch (ex) {
        this.$message.error(ex + "");
      }
    },

    doJsonImportBack(json) {
      try {
        let importObj = JSON.parse(json || this.importTemplate);
        this.designer.loadFormJson(importObj);

        this.showImportJsonDialogFlag = false;
        this.$message.success(
          this.i18nt(json ? "替换成功" : "designer.hint.importJsonSuccess")
        );

        this.designer.emitHistoryChange();

        this.designer.emitEvent("form-json-imported", []);
      } catch (ex) {
        this.$message.error(ex + "");
      }
    },

    exportJson() {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      this.jsonContent = JSON.stringify({ widgetList, formConfig }, null, "  ");
      this.jsonRawContent = JSON.stringify({ widgetList, formConfig });
      this.showExportJsonDialogFlag = true;
    },

    copyFormJson(e) {
      copyToClipboard(
        this.jsonRawContent,
        e,
        this.$message,
        this.i18nt("designer.hint.copyJsonSuccess"),
        this.i18nt("designer.hint.copyJsonFail")
      );
    },

    saveFormJson() {
      this.saveAsFile(this.jsonContent, `vform${generateId()}.json`);
    },

    exportCode() {
      this.vueCode = generateCode(this.formJson);
      this.htmlCode = generateCode(this.formJson, "html");
      this.showExportCodeDialogFlag = true;
    },

    copyVueCode(e) {
      copyToClipboard(
        this.vueCode,
        e,
        this.$message,
        this.i18nt("designer.hint.copyVueCodeSuccess"),
        this.i18nt("designer.hint.copyVueCodeFail")
      );
    },

    /*copyHtmlCode(e) {
      copyToClipboard(
        this.htmlCode,
        e,
        this.$message,
        this.i18nt("designer.hint.copyHtmlCodeSuccess"),
        this.i18nt("designer.hint.copyHtmlCodeFail")
      );
    },*/

    saveVueCode() {
      this.saveAsFile(this.vueCode, `vform${generateId()}.vue`);
    },

    saveHtmlCode() {
      this.saveAsFile(this.htmlCode, `vform${generateId()}.html`);
    },

    generateSFC() {
      loadBeautifier((beautifier) => {
        this.sfcCode = genSFC(
          this.designer.formConfig,
          this.designer.widgetList,
          beautifier
        );
        this.sfcCodeV3 = genSFC(
          this.designer.formConfig,
          this.designer.widgetList,
          beautifier,
          true
        );
        this.showExportSFCDialogFlag = true;
      });
    },
    generateSave() {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      updateTemplateInfoJson({
        formId: this.$route.params.formId,
        templateJson: JSON.stringify({ widgetList, formConfig }, null, "  "),
      }).then((res) => {
        this.$notify.success({ message: "保存成功" });
        // this.$router.back(-1)
      });
    },
    copyV2SFC(e) {
      copyToClipboard(
        this.sfcCode,
        e,
        this.$message,
        this.i18nt("designer.hint.copySFCSuccess"),
        this.i18nt("designer.hint.copySFCFail")
      );
    },

    copyV3SFC(e) {
      copyToClipboard(
        this.sfcCodeV3,
        e,
        this.$message,
        this.i18nt("designer.hint.copySFCSuccess"),
        this.i18nt("designer.hint.copySFCFail")
      );
    },

    saveV2SFC() {
      this.saveAsFile(this.sfcCode, `vformV2-${generateId()}.vue`);
    },

    saveV3SFC() {
      this.saveAsFile(this.sfcCodeV3, `vformV3-${generateId()}.vue`);
    },

    getFormData() {
      this.$refs["preForm"]
        .getFormData()
        .then((formData) => {
          this.formDataJson = JSON.stringify(formData, null, "  ");
          this.formDataRawJson = JSON.stringify(formData);

          this.showFormDataDialogFlag = true;
        })
        .catch((error) => {
          this.$message.error(error);
        });
    },

    copyFormDataJson(e) {
      copyToClipboard(
        this.formDataRawJson,
        e,
        this.$message,
        this.i18nt("designer.hint.copyJsonSuccess"),
        this.i18nt("designer.hint.copyJsonFail")
      );
    },

    saveFormData() {
      this.saveAsFile(this.htmlCode, `formData${generateId()}.json`);
    },

    resetForm() {
      this.$refs["preForm"].resetForm();
    },

    setFormDisabled() {
      this.$refs["preForm"].disableForm();
    },

    setFormEnabled() {
      this.$refs["preForm"].enableForm();
    },

    handleFormChange(fieldName, newValue, oldValue, formModel) {
      console.log("---formChange start---");
      console.log("fieldName", fieldName);
      console.log("newValue", newValue);
      console.log("oldValue", oldValue);
      console.log("formModel", formModel);
      console.log("---formChange end---");
    },

    testOnAppendButtonClick(clickedWidget) {
      console.log("test", clickedWidget);
    },

    testOnButtonClick(button) {
      console.log("test", button);
    },
  },
  created() {
    clearInterval(this.timer);
    this.timer = setInterval(() => {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      let formId = "";
      try {
        formId = this.formObj.formId;
      } catch (error) {}
      saveBackupData({
        json: JSON.stringify({ widgetList, formConfig }, null, "  "),
        module: "view",
        templateId: this.templateId,
      });
    }, 15000);
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  padding: 0 5px;
}
.toolbar-container {
  padding-top: 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .side-scroll-bar .el-scrollbar__wrap {
  overflow-x: hidden;
  height: calc(100vh - 220px);
}
// ::v-deep .side-scroll-bar .is-horizontal {
//   display: none;
// }
.left-toolbar {
  font-size: 16px;
  ::v-deep .el-button--text {
    padding: 10px !important;
  }
}

.right-toolbar {
  ::v-deep .el-button--text {
    font-size: 14px !important;
    padding: 10px 0 !important;
  }
}

.el-button i {
  margin-right: 3px;
}

.small-padding-dialog {
  ::v-deep .el-dialog.is-fullscreen {
    //padding-top: 3px;
    //padding-bottom: 3px;
    // background: #f1f2f3;
    left: 0 !important;
    transform: translateX(15px);
  }

  ::v-deep .el-dialog__body {
    padding: 12px 15px 12px 15px;

    .el-alert.alert-padding {
      padding: 0 10px;
    }
  }

  ::v-deep .ace-container {
    border: 1px solid #dcdfe6;
  }
}

.dialog-title-light-bg {
  ::v-deep .el-dialog__header {
    background: #f1f2f3;
  }
}

.no-box-shadow {
  box-shadow: none;
}

.no-padding.el-tabs--border-card {
  ::v-deep .el-tabs__content {
    padding: 0;
  }
}

.form-render-wrapper {
  //height: calc(100vh - 142px);
  all: revert !important; /* 防止表单继承el-dialog等外部样式，未生效，原因不明？？ */
}

.form-render-wrapper.h5-layout {
  margin: 0 auto;
  width: 420px;
  border-radius: 15px;
  //border-width: 10px;
  box-shadow: 0 0 1px 10px #495060;
  height: calc(100vh - 142px);
}

/* HTML预览对话框样式 */
.html-preview-dialog {
  ::v-deep .el-dialog {
    margin-top: 5vh !important;
  }

  ::v-deep .el-dialog__body {
    padding: 10px 20px;
  }
}

.html-preview-container {
  .preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .preview-only, .code-only {
    height: 500px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }

  .html-preview-frame {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
  }

  .split-view {
    display: flex;
    height: 500px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;

    .split-left, .split-right {
      flex: 1;
      display: flex;
      flex-direction: column;

      h4 {
        margin: 0;
        padding: 10px 15px;
        background: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
        font-size: 14px;
        color: #606266;
      }
    }

    .split-left {
      border-right: 1px solid #e4e7ed;
    }

    .html-preview-frame {
      flex: 1;
      background: white;
    }
  }
}

/* 代码编辑器样式调整 */
::v-deep .ace-container {
  border: none !important;
}
</style>
