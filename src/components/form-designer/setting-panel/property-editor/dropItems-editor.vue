<template>
  <div>
    <el-form-item label="按钮形式">
      <el-switch v-model="optionModel.isButton"></el-switch>
    </el-form-item>
    <el-form-item v-if="optionModel.isButton" label="按钮类型">
      <el-select v-model="optionModel.dropType">
        <el-option label="primary" value="primary"></el-option>
        <el-option label="danger" value="danger"></el-option>
        <el-option label="warning" value="warning"></el-option>
        <el-option label="info" value="info"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="optionModel.isButton" label="按钮尺寸">
      <el-select v-model="optionModel.dropSize">
        <el-option label="medium" value="medium"></el-option>
        <el-option label="small" value="small"></el-option>
        <el-option label="mini" value="mini"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="弹出位置">
      <el-select v-model="optionModel.placement">
        <el-option label="top" value="top"></el-option>
        <el-option label="top-start" value="top-start"></el-option>
        <el-option label="top-end" value="top-end"></el-option>
        <el-option label="bottom" value="bottom"></el-option>
        <el-option label="bottom-start" value="bottom-start"></el-option>
        <el-option label="bottom-end" value="bottom-end"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="触发方式">
      <el-select v-model="optionModel.trigger">
        <el-option label="悬浮" value="hover"></el-option>
        <el-option label="点击" value="click"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="点击后隐藏菜单">
      <el-switch v-model="optionModel.hideOnClick"></el-switch>
    </el-form-item>
    <el-form-item label="菜单项配置">
      <el-button
        type="primary"
        icon="el-icon-edit"
        plain
        round
        roundsteps
        @click="dialogShow = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      title="菜单项配置"
      width="70%"
      :visible="dialogShow"
      :show-close="false"
    >
      <lt-sort-table
        v-model="optionModel.dropItems"
        max-height="500"
        row-key="id"
        :isEnable="true"
      >
        <lt-table-column-edit prop="label" label="标签"></lt-table-column-edit>
        <lt-table-column-edit
          prop="command"
          label="指令"
        ></lt-table-column-edit>
        <lt-table-column-edit prop="icon" label="图标">
          <template #header>
            图标
            <el-tooltip effect="light" content="只支持el-icon">
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="disabled" state="switch" label="禁用" />
        <lt-table-column-edit prop="divided" state="switch" label="分割线" />
        <lt-table-column-operation
          v-model="optionModel.dropItems"
          :row-data="rowObj"
          :unshift="false"
          ref="operationRef"
          @save="save"
          @addBefore="addBefore"
          :unlimitedAdd="true"
          :validateProp="false"
          primary-key="id"
          :layout="['delete', 'edit', 'save']"
        >
        </lt-table-column-operation>
      </lt-sort-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false"> 取消</el-button>
        <el-button type="primary" @click="dialogShow = false"> 确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin";
import IconSelect from "@/components/IconSelect/index.vue";

export default {
  name: "dropItems-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { IconSelect },
  data() {
    return {
      predefineColors: ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#909399"],
      dialogShow: false,
      rowObj: {
        id: "",
        label: "",
        command: "",
        icon: "",
        disabled: false,
        divided: false,
      },
    };
  },

  methods: {
    addBefore() {
      this.rowObj.id = +new Date().getTime();
    },
    save({ data }) {
      this.$notify.success({ message: "保存成功" });
      this.$refs.operationRef.saveComplete(data);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
