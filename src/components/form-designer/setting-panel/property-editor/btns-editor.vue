<template>
  <div>
    <el-form-item label="操作列配置">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        round
        @click="dialogVisible = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      title="操作列配置"
      width="80%"
      :visible="dialogVisible"
      :show-close="false"
    >
      <div v-if="optionModel.rowKey" style="margin-bottom: 10px">
        <span>行内编辑:</span>
        <el-switch
          style="margin-left: 20px"
          @change="onChange"
          v-model="optionModel.isEdit"
        ></el-switch>
      </div>

      <lt-sort-table
        v-model="optionModel.btns"
        isEnable
        max-height="500"
        row-key="cust-id"
      >
        <lt-table-column-edit
          prop="type"
          label="显示类型"
          state="select"
          :select-opt="{
            options: [
              { label: '文字', value: 1 },
              { label: '图标', value: 2 },
            ],
            label: 'label',
            value: 'value',
          }"
          @change="selectChange"
        />
        <lt-table-column-edit prop="name" label="名称">
          <template #preview="{ row }">
            <span v-if="row.type === 1" :style="{ color: row.color }">{{
              row.name
            }}</span>
            <template v-else>
              <i
                v-if="row.name && row.name.includes('el-icon')"
                :style="{ color: row.color }"
                :class="row.name"
              ></i>
              <svg-icon
                v-else
                :icon-class="row.name"
                :style="{ color: row.color }"
              />
            </template>
          </template>
          <template #edit="{ row, $index }">
            <el-input
              v-if="row.type === 1"
              v-model.trim="row.name"
              placeholder="请输入名称"
            />
            <template v-else>
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect
                  ref="iconSelect"
                  @selected="selected($event, $index)"
                />
                <el-input
                  slot="reference"
                  v-model="row.name"
                  placeholder="点击选择图标"
                  readonly
                >
                  <svg-icon
                    v-if="row.name"
                    slot="prefix"
                    :icon-class="row.name"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                  <i
                    v-else
                    slot="prefix"
                    class="el-icon-search el-input__icon"
                  />
                </el-input>
              </el-popover>
            </template>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="color" label="颜色">
          <template #header>
            颜色<el-tooltip
              effect="light"
              content="支持16进制(例: #000)和rgb(例: rgb(255,255,255))"
            >
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <template #edit="{ row }">
            <template>
              <el-color-picker
                v-model="row.color"
                show-alpha
                :predefine="predefineColors"
                size="mini"
              ></el-color-picker>
            </template>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="tip" label="提示信息" />
        <lt-table-column-edit prop="fun" label="点击事件" edit-disable>
          <template #preview="{ $index }">
            <el-link
              :underline="false"
              type="primary"
              @click="showCodeDialog('点击事件', $index, 'fun')"
              >编辑点击事件</el-link
            >
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="displayFun" label="显示条件" edit-disable>
          <template #preview="{ $index }">
            <el-link
              :underline="false"
              type="primary"
              @click="showCodeDialog('显示条件', $index, 'displayFun')"
              >编辑显示条件</el-link
            >
          </template>
        </lt-table-column-edit>
        <lt-table-column-operation
          ref="operationRef"
          width="160"
          v-model="optionModel.btns"
          :row-data="rowObj"
          @addBefore="addBefore"
          :unshift="false"
          validateProp="type, name"
          primary-key="cust-id"
          unlimitedAdd
          @save="custSave"
          @delete="custDelete"
        />
      </lt-sort-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkDialog"> 确认</el-button>
      </div>

      <el-dialog
        :title="codeInterface.desc"
        width="60%"
        :visible="codeVisible"
        :show-close="false"
      >
        <div class="d-flex">
          <componentTree ref="componentTreeRef" />
          <div class="flex-1">
             <el-alert type="info" :closable="false" title="(row,index)=>{" />
            <code-editor
              v-model="codeInterface.code"
              mode="javascript"
              :readonly="false"
              :key="codeEditor"
            />
            <el-alert type="info" :closable="false" title="}" />
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="codeVisible = false"> 取消</el-button>
          <el-button type="primary" @click="codeConfirm"> 确认</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import CodeEditor from '@/components/code-editor/index.vue';
import componentTree from '../components/componentTree.vue';
import IconSelect from '@/components/IconSelect/index.vue';
export default {
  name: 'btns-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { CodeEditor, componentTree, IconSelect },
  data() {
    return {
      isEdit: false,
      predefineColors: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'],
      dialogVisible: false,
      rowObj: {
        'cust-id': '',
        type: 1,
        name: '',
        tip: '',
        fun: '',
        color: '',
        displayFun: '',
      },
      codeInterface: {
        code: '',
        index: -1,
        desc: '',
        key: '',
      },
      codeVisible: false,
      codeEditor: '',
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        val.btns &&
          val.btns.forEach((item) => {
            if (!('type' in item)) this.$set(item, 'type', 1);
            if (!('tip' in item)) this.$set(item, 'tip', '');
            if (!('color' in item)) this.$set(item, 'color', '');
          });
      },
      deep: true,
    },
  },
  mounted() {
    if (!this.optionModel.hasOwnProperty('isEdit')) {
      this.$set(this.optionModel, 'isEdit', false);
    }
    this.optionModel.btns.forEach((item) => {
      if (!item['cust-id']) this.$set(item, 'cust-id', this.$string.getUUID(7));
    });
  },
  methods: {
    onChange(val) {
      if (val) {
        this.optionModel.btns.unshift(
          {
            'cust-id': 'edit',
            type: 2,
            name: '编辑',
            tip: '编辑',
            fun: `this.getWidgetRef('${this.selectedWidget.id}').setRowEditable(row.${this.selectedWidget.options.rowKey}, true, (obj) => {
  /**
   * 编辑前置操作
   * @param { obj } Object 当前行的值
   * @return true 默认返回true
   **/

  // return false    // 终止编辑
})`,
            color: '',
            displayFun: 'return !row.isEdit ',
          },
          {
            'cust-id': 'save',
            type: 2,
            name: '保存',
            tip: '保存',
            fun: `this.getWidgetRef('${this.selectedWidget.id}').setRowEditable(row.${this.selectedWidget.options.rowKey}, false, (obj) => {
  /**
   * 保存前置操作
   * @param { obj } Object 当前行的值
   * @return true 默认返回true
   **/

  // return false    // 终止保存
})`,
            color: '',
            displayFun: 'return row.isEdit ',
          },
        );
      } else {
        this.optionModel.btns = this.optionModel.btns.filter(
          (item) => !['edit', 'save'].includes(item['cust-id']),
        );
      }
    },
    addBefore() {
      this.rowObj['cust-id'] = this.$string.getUUID(7);
    },
    showCodeDialog(desc, index, key) {
      this.codeInterface = {
        code: this.optionModel.btns[index][key],
        index,
        desc,
        key,
      };
      this.codeVisible = true;
      this.codeEditor = Math.random();
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },
    codeConfirm() {
      let { index, code, key } = this.codeInterface;
      this.optionModel.btns[index][key] = code;
      this.codeVisible = false;
    },
    custSave({ data }) {
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    custDelete({ index }) {
      this.optionModel.btns.splice(index, 1);
      this.$notify.success({ message: '删除成功' });
    },
    // 选择图标
    selected(e, index) {
      this.optionModel.btns[index].name = e;
    },
    selectChange({ index }) {
      this.optionModel.btns[index].name = '';
    },
    checkDialog() {
      for (let i = 0; i < this.optionModel.btns.length; i++) {
        if (this.optionModel.btns[i].isEdit) {
          this.$message({ message: `请保存第${i + 1}条数据` });
          return;
        }
      }
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;

  margin: 5px 0;
}

::v-deep .el-button--mini.is-circle {
  padding: 5px;
}
::v-deep .el-input__prefix {
  top: -4px;
}
</style>
