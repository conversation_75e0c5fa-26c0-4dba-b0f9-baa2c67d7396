<template>
<div>
   <el-form-item label="当前步骤状态">
    <el-select v-model="optionModel.processStatus">
      <el-option label="process" value="process"></el-option>
      <el-option label="wait" value="wait"></el-option>
      <el-option label="success" value="success"></el-option>
      <el-option label="finish" value="finish"></el-option>
      <el-option label="error" value="error"></el-option>
    </el-select>
  </el-form-item>
  <el-form-item label="结束步骤状态">
    <el-select v-model="optionModel.finishStatus">
      <el-option label="process" value="process"></el-option>
      <el-option label="wait" value="wait"></el-option>
      <el-option label="success" value="success"></el-option>
      <el-option label="finish" value="finish"></el-option>
      <el-option label="error" value="error"></el-option>
    </el-select>
  </el-form-item>
  <el-form-item label="居中">
    <el-switch v-model="optionModel.alignCenter"></el-switch>
  </el-form-item>
  <el-form-item label="简洁风格">
    <el-switch v-model="optionModel.simple"></el-switch>
  </el-form-item>
  <el-form-item label="间隙">
    <el-input v-model="optionModel.space"></el-input>
  </el-form-item>
  <el-form-item label="步骤配置">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        roundsteps
        @click="dialogShow = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      title="步骤配置"
      width="60%"
      :visible="dialogShow"
      :show-close="false"
    >
      <lt-sort-table v-model="optionModel.steps" :isEnable="true" max-height="500" row-key="id">
        <lt-table-column-edit prop="title" label="标题"></lt-table-column-edit>
        <lt-table-column-edit prop="description" label="描述"></lt-table-column-edit>
        <lt-table-column-edit prop="icon" label="图标">
          <template #preview="{ row }">
              <i v-if="row.icon&&row.icon.includes('el-icon')" :class="row.icon"></i>
              <svg-icon
              v-else
              :icon-class="row.icon"
              :style="{ color: row.color }"
            />
          </template>
          <template #edit="{ row }">
            <template>
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect
                  ref="iconSelect"
                  @selected="selected($event, row)"
                />
                <el-input
                  slot="reference"
                  readonly
                >
                  <svg-icon
                    v-if="row.icon"
                    slot="prefix"
                    :icon-class="row.icon"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                </el-input>
              </el-popover>
            </template>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="color" label="图标颜色">
          <template #header>
            图标颜色<el-tooltip
              effect="light"
              content="支持16进制(例: #000)和rgb(例: rgb(255,255,255))"
            >
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <template #edit="{ row}">
            <template>
                <el-color-picker v-model="row.color" show-alpha
                     :predefine="predefineColors" size="mini"></el-color-picker>
            </template>
          </template>
        </lt-table-column-edit>
        <lt-table-column-operation
          v-model="optionModel.steps"
          :row-data="rowObj"
          :unshift="false"
          ref="operationRef"
           @save="save"
          @addBefore="addBefore"
          :unlimitedAdd="true"
          :validateProp="false"
          primary-key="id"
          :layout="['delete', 'edit', 'save']"
        >
        </lt-table-column-operation>
      </lt-sort-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false"> 取消</el-button>
        <el-button type="primary" @click="dialogShow = false"> 确认</el-button>
      </div>
    </el-dialog>

</div>

</template>

<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
import IconSelect from '@/components/IconSelect/index.vue';

  export default {
    name: "steps-editor",
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
     components: { IconSelect },
    data(){
      return {
           predefineColors: [
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
        ],
        dialogShow:false,
        rowObj: {
          id:'',
        title: '',
        description: '',
        icon:'',
        color:"",
        isEdit:true
      },
      }
    },
    methods:{
      selected(e, row) {
      row.icon=e
    },
      addBefore() {
         this.rowObj.id = +new Date().getTime();
      },
       save({ data }) {
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    }
  }
</script>

<style lang="scss" scoped>

</style>
