<template>
  <div v-if="!optionModel.hasOwnProperty('isCustom') || !optionModel.isCustom">
    <el-form-item :label="optionModel.hasOwnProperty('isCustom')?'底部按钮配置':'表格左侧按钮'">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        round
        @click="dialogVisible = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      title="按钮配置"
      width="80%"
      :visible="dialogVisible"
      :show-close="false"
    >
      <lt-sort-table  :isEnable="true" row-key="cust-id" v-model="optionModel.leftBtns" max-height="500">
        <lt-table-column-edit prop="name" label="名称" />
        <lt-table-column-edit
          prop="type"
          label="类型"
          state="select"
          :selectOpt="{
            options: [
              { label: 'default', value: 'default' },
              { label: 'primary', value: 'primary' },
              { label: 'success', value: 'success' },
              { label: 'warning', value: 'warning' },
              { label: 'danger', value: 'danger' },
              { label: 'info', value: 'info' },

            ],
            label: 'label',
            value: 'value',
          }"
        />
        <lt-table-column-edit prop="icon" label="图标">
          <template #preview="{ row }">
            <i v-if="row.icon&&row.icon.includes('el-icon')" :class="row.icon"></i>
              <svg-icon
              v-else
              :icon-class="row.icon"
              :style="{ color: row.color }"
            />
          </template>
          <template #edit="{ row, $index }">
            <el-popover
              placement="bottom-start"
              width="460"
              trigger="click"
              @show="$refs['iconSelect'].reset()"
            >
              <IconSelect
                ref="iconSelect"
                @selected="selected($event, $index)"
              />
              <el-input
                slot="reference"
                placeholder="点击选择图标"
                readonly
              >
                <svg-icon
                  v-if="row.icon"
                  slot="prefix"
                  :icon-class="row.icon"
                  class="el-input__icon"
                  style="height: 32px; width: 16px"
                />
                <i v-else slot="prefix" class="el-icon-search el-input__icon" />
              </el-input>
            </el-popover>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="color" label="图标颜色">
          <template #header>
            图标颜色<el-tooltip
              effect="light"
              content="支持16进制(例: #000)和rgb(例: rgb(255,255,255))"
            >
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <template #edit="{ row}">
            <template>
                <el-color-picker v-model="row.color" show-alpha
                     :predefine="predefineColors" size="mini"></el-color-picker>
            </template>
          </template>
        </lt-table-column-edit>
<!--
        <lt-table-column-edit prop="bgColor" label="背景色">
          <template #header>
            背景色<el-tooltip
              effect="light"
              content="非必填,优先级大于类型,支持16进制(例: #000)和rgb(例: rgb(255,255,255))"
            >
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <template #edit="{ row}">
            <template>
                <el-color-picker v-model="row.bgColor" show-alpha
                     :predefine="predefineColors" size="mini"></el-color-picker>
            </template>
          </template>
        </lt-table-column-edit> -->

        <lt-table-column-edit prop="fun" label="点击事件" edit-disable>
          <template #preview="{ $index }">
            <el-link
              :underline="false"
              type="primary"
              @click="showCodeDialog('编写函数代码', $index, 'fun')"
              >编辑点击事件</el-link
            >
          </template>
        </lt-table-column-edit>

        <lt-table-column-edit prop="displayFun" label="显示条件" edit-disable>
          <template #preview="{ $index }">
            <el-link
              :underline="false"
              type="primary"
              @click="showCodeDialog('显示条件', $index, 'displayFun')"
              >编辑显示条件</el-link
            >
          </template>
        </lt-table-column-edit>

        <lt-table-column-operation
          ref="operationRef"
          width="160"
          v-model="optionModel.leftBtns"
          :row-data="rowObj"
          :unshift="false"
          validateProp="name, type"
          primary-key="cust-id"
          unlimitedAdd
          @addBefore="addBefore"
          @save="custSave"
          @delete="custDelete"
        />
      </lt-sort-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkDialog"> 确认</el-button>
      </div>

      <el-dialog
        :title="codeInterface.desc"
        width="60%"
        :visible="codeVisible"
        :show-close="false"
      >
        <div class="d-flex">
          <componentTree ref="componentTreeRef" />
          <div class="flex-1">
            <code-editor
              v-model="codeInterface.code"
              mode="javascript"
              :readonly="false"
              :key="codeEditor"
            />
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="codeVisible = false"> 取消</el-button>
          <el-button type="primary" @click="codeConfirm"> 确认</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import CodeEditor from '@/components/code-editor/index.vue';
import componentTree from '../components/componentTree.vue';
import IconSelect from '@/components/IconSelect/index.vue';
export default {
  name: 'leftBtns-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { CodeEditor, componentTree, IconSelect },
  data() {
    return {
      predefineColors: [
          '#409EFF',
          '#67C23A',
          '#E6A23C',
          '#F56C6C',
          '#909399',
        ],
      dialogVisible: false,
      rowObj: {
        'cust-id': '',
        name: '',
        type: '',
        icon: '',
        color: '#FFFFFF',
        bgColor: '',
        fun: '',
        displayFun: '',
      },
      codeInterface: {
        code: '',
        index: -1,
        desc: '',
        key: '',
      },
      codeVisible: false,
      codeEditor: '',
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.leftBtns) this.$set(val, 'leftBtns', []);
        // val.btns &&
        //   val.btns.forEach((item) => {
        //     if (!('type' in item)) this.$set(item, 'type', 1);
        //     if (!('cust-id' in item)) this.$set(item, 'cust-id', Math.random());
        //     if (!('tip' in item)) this.$set(item, 'tip', '');
        //     if (!('color' in item)) this.$set(item, 'color', '');
        //   });
      },
      deep: true,
    },
  },
  methods: {
    addBefore() {
      this.rowObj['cust-id'] = this.$string.getUUID(7);
    },
    showCodeDialog(desc, index, key) {
      this.codeInterface = {
        code: this.optionModel.leftBtns[index][key],
        index,
        desc,
        key,
      };
      this.codeVisible = true;
      this.codeEditor = Math.random();
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },
    codeConfirm() {
      let { index, code, key } = this.codeInterface;
      this.optionModel.leftBtns[index][key] = code;
      this.codeVisible = false;
    },
    custSave({ data }) {
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    custDelete({ index }) {
      this.optionModel.leftBtns.splice(index, 1);
      this.$notify.success({ message: '删除成功' });
    },
    // 选择图标
    selected(e, index) {
      this.optionModel.leftBtns[index].icon = e;
    },

    checkDialog() {
      for (let i = 0; i < this.optionModel.leftBtns.length; i++) {
        console.log(this.optionModel.leftBtns[i].isEdit);
        if (this.optionModel.leftBtns[i].isEdit) {
          this.$message({ message: `请保存第${i + 1}条数据` });
          return;
        }
      }
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;

  margin: 5px 0;
}

::v-deep .el-button--mini.is-circle {
  padding: 5px;
}
::v-deep .el-input__prefix {
  top: -4px;
}
</style>
