import { deepClone } from '@/utils/util';
import FormValidators from '@/utils/validators';
import { executeInterface } from '@/api/interfaces/interfaces';
import request from '@/utils/request';
import ChinesePY from '@/utils/chinesePY.js';
import bus from '@/scripts/bus.js';
import { getToken } from '@/utils/auth';
import axios from 'axios';
export default {
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  computed: {
    subFormName() {
      return this.parentWidget ? this.parentWidget.options.name : '';
    },

    subFormItemFlag() {
      return this.parentWidget ? this.parentWidget.type === 'sub-form' : false;
    },
    subTableItemFlag() {
      return this.parentWidget
        ? this.parentWidget.type === 'data-table'
        : false;
    },
    subFormGroupFlag() {
      return (this.parentWidget ? this.parentWidget.type === 'sub-form' : false)
        ? this.parentWidget.groupPage
        : false;
    },
    trendsTabFormItemFlag() {
      return this.parentWidget
        ? this.parentWidget.type === 'trends-tab'
        : false;
    },
    formModel: {
      cache: false,
      get() {
        return this.globalModel.formModel;
      },
    },
  },
  methods: {
    getRequest(pp) {
      return request(pp);
    },
    waitHttp(apiId, data) {
      let settings = {
        url: `${import.meta.env.VITE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
        async: false,
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';

      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings).responseJSON;
    },
    asyncHttp(apiId, data) {
      let settings = {
        url: `${import.meta.env.VITE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';
      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings);
    },
    // --------------------- 组件内部方法 begin ------------------//
    openVfDialog(hasOpen, formId, data) {
      bus.$emit('onOpenVfDialog', hasOpen, formId, data);
    },

    getTextInitial(text = '') {
      let val = text || this.getValue();
      val = val + ''; // 转string
      let result = '';
      let reg = /[\u4e00-\u9fa5]+/;
      val.split('').forEach((item) => {
        if (item) {
          if (reg.test(item)) {
            result += ChinesePY.getWordsCode(item);
          } else {
            result += item;
          }
        }
      });
      return result.substr(0, 6);
    },
    initAsData() {
      // if (this.field.options.alias) {
      //   if (this.globalModel.asData in this.field.options.alias) {
      //     this.fieldModel = this.globalModel.asData[this.field.options.alias]
      //   } else {
      //     this.$set(this.globalModel.asData, this.field.options.alias, this.fieldModel)
      //   }
      //   this.$set(this.globalModel.mapping, this.field.options.alias, this.field.options.name)
      // }
    },
    // asData() {
    //   return this.globalModel.asData
    // },
    // set(key, value) {
    //   this.getWidgetRef(this.globalModel.mapping[key]).setValue(value)
    //   this.$set(this.globalModel.asData, key, value)
    // },
    initFieldModel() {
      if (this.field.formItemFlag === false) {
        return;
      }

      if (this.parentWidget ? this.parentWidget.type === 'trends-tab' : false) {
        // 获取数据
        let subFormData = this.formModel[this.subFormName];
        this.fieldModel =
          subFormData[this.subFormRowIndex][this.field.options.name];
        // this.subFormRowId = parseInt(this.subFormRowIndex)
        this.initAsData();
        return;
      }
      if (this.parentWidget && this.parentWidget.type == 'data-table') {
        let tableKye = this.field.options.alias || this.field.id;
        let subFormData = this.formModel[this.subFormName];

        if (
          (subFormData === undefined ||
            subFormData[this.subFormRowIndex] === undefined ||
            subFormData[this.subFormRowIndex][tableKye] === undefined) &&
          this.field.options.defaultValue !== undefined
        ) {
          // 为空设置默认值
          this.fieldModel = this.field.options.defaultValue;
          subFormData[this.subFormRowIndex][tableKye] =
            this.field.options.defaultValue;
        } else if (
          // 子表单数据中无数据
          subFormData[this.subFormRowIndex][tableKye] === undefined
        ) {
          this.fieldModel = [];
          subFormData[this.subFormRowIndex][tableKye] = this.fieldModel;
        } else {
          this.fieldModel = subFormData[this.subFormRowIndex][tableKye];
        }
        // this.fieldModel = this.formModel[this.subFormName][this.subFormRowIndex][this.field.options.alias || this.field.id]
        // this.setValue(this.formModel[this.subFormName][this.subFormRowIndex][this.field.options.alias])
        return;
      }

      if (!!this.subFormItemFlag && !this.designState) {
        // SubForm子表单组件需要特殊处理！！
        let subFormData = this.formModel[this.subFormName];
        if (
          (subFormData === undefined ||
            subFormData[this.subFormRowIndex] === undefined ||
            subFormData[this.subFormRowIndex][this.field.options.name] ===
              undefined) &&
          this.field.options.defaultValue !== undefined
        ) {
          // 为空设置默认值
          this.fieldModel = this.field.options.defaultValue;
          subFormData[this.subFormRowIndex][this.field.options.name] =
            this.field.options.defaultValue;
        } else if (
          // 子表单数据中无数据
          subFormData[this.subFormRowIndex][this.field.options.name] ===
          undefined
        ) {
          this.fieldModel = null;
          subFormData[this.subFormRowIndex][this.field.options.name] = null;
        } else {
          this.fieldModel =
            subFormData[this.subFormRowIndex][this.field.options.name];
        }

        /* 主动触发子表单内field-widget的onChange事件！！ */
        setTimeout(() => {
          // 延时触发onChange事件, 便于更新计算字段！！
          this.handleOnChangeForSubForm(
            this.fieldModel,
            this.oldFieldValue,
            subFormData,
            this.subFormRowId,
          );
        }, 800);
        this.oldFieldValue = deepClone(this.fieldModel);

        this.initFileList(); // 处理图片上传、文件上传字段

        return;
      }

      if (
        this.formModel[this.field.options.name] === undefined &&
        this.field.options.defaultValue !== undefined
      ) {
        this.fieldModel = this.field.options.defaultValue;
      } else if (this.formModel[this.field.options.name] === undefined) {
        // 如果formModel为空对象，则初始化字段值为null!!
        this.formModel[this.field.options.name] = null;
      } else {
        this.fieldModel = this.formModel[this.field.options.name];
      }
      this.oldFieldValue = deepClone(this.fieldModel);
      this.initFileList(); // 处理图片上传、文件上传字段
    },

    // 当fieldModel 不符合组件值时候调用进行解析
    initFileList() {
      // 时间范围组件
      if (this.field.type == 'time-range' || this.field.type == 'date-range') {
        if (
          this.fieldModel == '' ||
          this.fieldModel == null ||
          this.fieldModel == []
        ) {
          this.$set(this, 'processValue', []);
        } else {
          if (typeof this.fieldModel == 'string') {
            this.$set(this, 'processValue', this.fieldModel.split(','));
          }
        }
      } else if (this.field.type == 'select') {
        if (
          this.fieldModel !== '' &&
          this.fieldModel !== null &&
          this.fieldModel !== undefined
        ) {
          if (Array.isArray(this.fieldModel)) {
            this.fieldModel.forEach((item, index) => {
              this.fieldModel[index] = item.toString();
            });
          } else {
            this.fieldModel = this.fieldModel.toString();
          }
          if (this.selectData && this.selectData.length > 0) {
            this.setSelectItem(
              this.selectData.filter(
                (item) =>
                  item[this.field.options.selectValue] === this.fieldModel,
              )[0],
            );
          }
        }

        // this.splitValues()
      } else if (this.field.type == 'checkbox') {
        this.splitValues();
        this.judgeAllCheck();
      } else if (this.field.type == 'data-table') {
        // this.copyData = this.formModel
      } else if (this.field.type == 'cascader') {
        // TODO 从formModel获取值就需要考虑subform的情况
        if (
          this.formModel[this.field.options.name] &&
          typeof this.formModel[this.field.options.name] == 'string'
        ) {
          this.formModel[this.field.options.name] = JSON.parse(
            this.formModel[this.field.options.name],
          );
          this.fieldModel = this.formModel[this.field.options.name];
        }
      } else if (this.field.type == 'switch') {
        // 判断值的类型
        if (this.fieldModel !== null && this.fieldModel !== '') {
          let valueType = typeof this.fieldModel;
          const { activeValue, inactiveValue } = this.field.options;
          if (!activeValue && !inactiveValue) {
            if (valueType === 'string') {
              if (['0', '1'].includes(this.fieldModel)) {
                this.$set(this.field.options, 'activeValue', '1');
                this.$set(this.field.options, 'inactiveValue', '0');
              }
              if (['true', 'false'].includes(this.fieldModel)) {
                this.$set(this.field.options, 'activeValue', 'true');
                this.$set(this.field.options, 'inactiveValue', 'false');
              }
            } else if (this.valueType == 'number') {
              this.$set(this.field.options, 'activeValue', 1);
              this.$set(this.field.options, 'inactiveValue', 0);
            }
          }
        }
      } else if (this.field.type == 'select-page') {
        this.sendApi();
      } else if (this.field.type == 'popup-select') {
        if (this.field.options.bandDataIdsApi && this.fieldModel) {
          executeInterface({
            apiId: this.field.options.bandDataIdsApi,
            body: { ids: this.fieldModel },
          }).then((e) => {
            this.fieldName = e.data[this.fieldModel];
          });
        } else {
          this.fieldName = null;
        }
      }
    },
    // 当组件绑定的非fieldModel 触发修改反向修改 当组件绑定的非fieldModel
    initFieldModelList() {
      // 复选框，选择修改的是checkboxModel
      if (this.field.type == 'checkbox') {
        this.setValue(this.checkboxModel.join());
      }
    },
    getAlias() {
      return this.field.options.alias;
    },

    initEventHandler() {
      this.$on('setFormData', function (newFormData) {
        if (!this.subFormItemFlag) {
          this.setValue(newFormData[this.field.options.name]);
        }
      });

      this.$on('field-value-changed', function (values) {
        if (this.subFormItemFlag || this.trendsTabFormItemFlag) {
          let subFormData = this.formModel[this.subFormName];
          this.handleOnChangeForSubForm(
            values[0],
            values[1],
            subFormData,
            this.subFormRowId,
          );
        } else {
          this.handleOnChange(values[0], values[1]);
        }
      });
      /* 监听重新加载选项事件 */
      this.$on('reloadOptions', function (widgetNames) {
        if (
          widgetNames.length === 0 ||
          widgetNames.indexOf(this.field.options.name) > -1
        ) {
          this.initOptionItems(true);
        }
      });
    },

    handleOnCreated() {
      if (this.field.options.onCreated) {
        let customFunc = new Function(this.field.options.onCreated);
        customFunc.call(this);
      }
    },

    handleOnMounted() {
      if (this.field.options.onMounted) {
        let mountFunc = new Function(this.field.options.onMounted);
        mountFunc.call(this);
      }
    },

    handleOnBeforeDestroy() {
      if (this.field.options.onBeforeDestroy) {
        let customFunc = new Function(this.field.options.onBeforeDestroy);
        customFunc.call(this);
      }
    },

    registerToRefList(oldRefName) {
      if (this.refList !== null && !!this.field.options.name) {
        if (this.subFormItemFlag && !this.designState) {
          // 处理子表单元素（且非设计状态）
          if (oldRefName) {
            delete this.refList[oldRefName + '@row' + this.subFormRowId];
          }
          this.refList[this.field.options.name + '@row' + this.subFormRowId] =
            this;
        } else if (this.subTableItemFlag && !this.designState) {
          // 处理子表单元素（且非设计状态）
          if (oldRefName) {
            delete this.refList[oldRefName + '@row' + this.subFormRowId];
          }
          this.refList[this.field.options.name + '@row' + this.subFormRowId] =
            this;
        } else {
          if (oldRefName) {
            delete this.refList[oldRefName];
          }
          this.refList[this.field.options.name] = this;
        }
      }
    },

    unregisterFromRefList() {
      // 销毁组件时注销组件ref
      if (this.refList !== null && !!this.field.options.name) {
        let oldRefName = this.field.options.name;
        if (this.subFormItemFlag && !this.designState) {
          // 处理子表单元素（且非设计状态）
          delete this.refList[oldRefName + '@row' + this.subFormRowId];
        } else {
          delete this.refList[oldRefName];
        }
      }
    },

    initOptionItems(keepSelected) {
      if (this.designState) {
        return;
      }

      if (
        this.field.type === 'radio' ||
        this.field.type === 'checkbox' ||
        this.field.type === 'select' ||
        this.field.type === 'cascader'
      ) {
        if (
          !!this.globalOptionData &&
          this.globalOptionData.hasOwnProperty(this.field.options.name)
        ) {
          if (keepSelected) {
            this.reloadOptions(this.globalOptionData[this.field.options.name]);
          } else {
            this.loadOptions(this.globalOptionData[this.field.options.name]);
          }
        }
      }
    },

    refreshDefaultValue() {
      if (
        this.designState === true &&
        this.field.options.defaultValue !== undefined
      ) {
        this.fieldModel = this.field.options.defaultValue;
      }
    },

    clearFieldRules() {
      if (this.rules) {
        this.rules.splice(0, this.rules.length); // 清空已有
      } else {
        this.rules = [];
      }
    },
    buildFieldRules() {
      this.clearFieldRules(); // 清空已有
      if (this.field.options.required) {
        this.rules.push({
          required: true,
          trigger: ['blur', 'change'],
          message:
            this.field.options.validationHint ||
            this.i18nt('render.hint.fieldRequired'),
        });
      }

      if (this.field.options.validation) {
        let vldName = this.field.options.validation;
        if (FormValidators[vldName]) {
          this.rules.push({
            validator: FormValidators[vldName],
            trigger: ['blur', 'change'],
            label: this.field.options.label,
            errorMsg: this.field.options.validationHint,
          });
        } else {
          this.rules.push({
            validator: FormValidators['regExp'],
            trigger: ['blur', 'change'],
            regExp: vldName,
            label: this.field.options.label,
            errorMsg: this.field.options.validationHint,
          });
        }
      }

      if (this.field.options.onValidate) {
        let customFn = new Function(
          'rule',
          'value',
          'callback',
          this.field.options.onValidate,
        );
        this.rules.push({
          validator: customFn,
          trigger: ['blur', 'change'],
          label: this.field.options.label,
          than: this,
        });
      }
    },

    /**
     * 禁用字段值变动触发表单校验
     */
    disableChangeValidate() {
      if (!this.rules) {
        return;
      }

      this.rules.forEach((rule) => {
        if (rule.trigger) {
          rule.trigger.splice(0, rule.trigger.length);
        }
      });
    },

    /**
     * 启用字段值变动触发表单校验
     */
    enableChangeValidate() {
      if (!this.rules) {
        return;
      }

      this.rules.forEach((rule) => {
        if (rule.trigger) {
          rule.trigger.push('blur');
          rule.trigger.push('change');
        }
      });
    },

    disableOptionOfList(optionList, optionValue) {
      if (!!optionList && optionList.length > 0) {
        optionList.forEach((opt) => {
          if (opt.value === optionValue) {
            opt.disabled = true;
          }
        });
      }
    },

    enableOptionOfList(optionList, optionValue) {
      if (!!optionList && optionList.length > 0) {
        optionList.forEach((opt) => {
          if (opt.value === optionValue) {
            opt.disabled = false;
          }
        });
      }
    },

    // --------------------- 组件内部方法 end ------------------//

    // --------------------- 事件处理 begin ------------------//

    emitFieldDataChange(newValue, oldValue) {
      this.$emit('field-value-changed', [newValue, oldValue]);

      /* 必须用dispatch向指定父组件派发消息！！ */
      this.dispatch('VFormRender', 'fieldChange', [
        this.field.options.name,
        newValue,
        oldValue,
        this.subFormName,
        this.subFormRowIndex,
      ]);
    },

    syncUpdateFormModel(value) {
      if (this.designState) {
        return;
      }
      if (this.parentWidget ? this.parentWidget.type === 'trends-tab' : false) {
        let subFormData = this.formModel[this.parentWidget.id] || [{}];
        let subFormDataRow = subFormData[this.subFormRowIndex];
        subFormDataRow[this.field.options.name] = value;
        return;
      }
      if (this.subFormGroupFlag) {
        // 子表单分组方式
      } else if (this.subFormItemFlag) {
        let subFormData = this.formModel[this.subFormName] || [{}];
        let subFormDataRow = subFormData[this.subFormRowIndex];
        subFormDataRow[this.field.options.name] = value;
      } else {
        this.formModel[this.field.options.name] = value;
      }
    },
    // 在代码逻辑之外的change事件，自定义触发事件
    handleCustomChangeEvent() {
      // checkbox 全选修改事件触发
      if (this.field.type === 'checkbox') {
        let checkedCount = this.checkboxModel.length;
        this.checkAll = checkedCount === this.field.options.optionItems.length;
        this.isIndeterminate =
          checkedCount > 0 &&
          checkedCount < this.field.options.optionItems.length;
      }
    },
    // 框架固定触发修改事件
    handleChangeEvent(value) {
      // 对下拉分页特殊处理,下拉分页change单选时返回的是个对象,预期本应是value值,所以此处对其特殊处理
      if (this.field.type === 'select-page' && !this.field.options.multiple) {
        this.selectItem = deepClone(value);
        value = value.value;
      }

      // 对多选下拉树、多选下拉分页、时间范围选择进行特殊处理,数组转string
      if (
        ((this.field.type === 'tree' && this.field.options.multiple) ||
          (this.field.type === 'select-page' && this.field.options.multiple) ||
          (this.field.type === 'select' && this.field.options.multiple) ||
          this.field.type === 'date-range' ||
          this.field.type === 'checkbox') &&
        value
      ) {
        value = value.join();
      }
      this.initFieldModelList();
      this.handleCustomChangeEvent();
      // if (this.field.type === 'date-time') this.field.options.defaultValue = value
      this.syncUpdateFormModel(value);
      this.emitFieldDataChange(value, this.oldFieldValue);

      // number组件一般不会触发focus事件，故此需要手工赋值oldFieldValue！！
      this.oldFieldValue =
        deepClone(
          value,
        ); /* oldFieldValue需要在initFieldModel()方法中赋初值!! */

      if (this.field.type === 'select' && this.field.options.filterable)
        this.selectData = this.copySelectData;
    },

    handleFocusCustomEvent(event) {
      this.oldFieldValue = deepClone(this.fieldModel); // 保存修改change之前的值
      if (this.field.options.onFocus) {
        let customFn = new Function('event', this.field.options.onFocus);
        customFn.call(this, event);
      }
      if (this.field.type === 'select') {
        setTimeout(() => {
          this.selectData = JSON.parse(JSON.stringify(this.copySelectData));
        }, 100);
      }
    },

    handleBlurCustomEvent(event) {
      if (this.field.options.onBlur) {
        let customFn = new Function('event', this.field.options.onBlur);
        customFn.call(this, event);
      }
    },

    handleInputCustomEvent(value) {
      this.syncUpdateFormModel(value);

      if (this.field.options.onInput) {
        let customFn = new Function('value', this.field.options.onInput);
        customFn.call(this, value);
      }
    },

    emitAppendButtonClick() {
      /* 必须调用mixins中的dispatch方法逐级向父组件发送消息！！ */
      this.dispatch('VFormRender', 'appendButtonClick', [this]);
    },

    handleOnChange(val, oldVal) {
      // 自定义onChange事件
      if (this.field.options.onChange) {
        let changeFn = new Function(
          'value',
          'oldValue',
          this.field.options.onChange,
        );
        changeFn.call(this, val, oldVal);
      }
    },

    handleOnChangeForSubForm(val, oldVal, subFormData, rowId) {
      // 子表单自定义onChange事件
      if (this.field.options.onChange) {
        let changeFn = new Function(
          'value',
          'oldValue',
          'subFormData',
          'rowId',
          this.field.options.onChange,
        );
        changeFn.call(this, val, oldVal, subFormData, rowId);
      }
    },

    handleButtonWidgetClick() {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }

      if (this.fieldModel && this.fieldModel.definitionId) {
        this.$set(this.field.options, 'btnDialogVisible', true);
        return;
      }

      // 绑定了接口则执行接口
      if (this.field.options.api) {
        // 组织参数
        let param = {};
        if (this.field.options.paramConfig) {
          JSON.parse(this.field.options.paramConfig).forEach((item) => {
            if (item.type === 'keyValue') {
              if (item.value[1].includes('.')) {
                let containerForm = item.value[1].split('.')[0];
                let form = item.value[1].split('.')[1];

                // 当前点击的button的子表单中的索引
                param[item.key] =
                  this.formModel[containerForm][this.subFormRowIndex][form];
              } else {
                param[item.key] = this.formModel[item.value[1]] || '';
              }
            } else {
              param[item.key] = [];
              // 判断是否在子表单中
              if ('widget' in this.$parent.$parent.$parent) {
                // 取当前行
                let subformId = this.$parent.$parent.$parent.widget.id;
                let obj = {};
                item.subset.forEach((item2) => {
                  let objKey = item2.key.split('.')[1];
                  let formKey = item2.value[1].split('.')[1];
                  // this.subFormRowIndex: 当前subformItem的索引
                  obj[objKey] =
                    this.formModel[subformId][this.subFormRowIndex][formKey];
                });

                param[item.key].push(obj);
              } else {
                // 取所有
                let relation = {};
                item.subset.forEach((item2) => {
                  relation[item2.value[1].split('.')[1]] =
                    item2.key.split('.')[1];
                });
                let dataAssemble = (data) => {
                  let obj = {};
                  for (let i in data) {
                    obj[relation[i]] = data[i];
                  }
                  return obj;
                };

                this.formModel[item.value[1]].forEach((item2) => {
                  param[item.key].push(dataAssemble(item2));
                });
              }
            }
          });
        }
        this.btnLoading = true;
        executeInterface({
          apiId: this.field.options.api,
          body: param,
        })
          .then((res) => {
            this.btnLoading = false;
            this.setValue(res.data);
            if (this.field.options.onSuccessCallback) {
              let success = new Function(
                'res',
                this.field.options.onSuccessCallback,
              );
              success.call(this, res);
            }
          })
          .catch((e) => {
            this.btnLoading = false;
            if (this.field.options.onFailCallback) {
              let fail = new Function('e', this.field.options.onFailCallback);
              fail.call(this, e);
            }
          });
        return;
      }

      if (this.field.options.onClick) {
        let changeFn = new Function(this.field.options.onClick);
        changeFn.call(this);
      } else {
        this.dispatch('VFormRender', 'buttonClick', [this]);
      }
    },

    remoteQuery(keyword) {
      if (this.field.options.onRemoteQuery) {
        let remoteFn = new Function(
          'keyword',
          this.field.options.onRemoteQuery,
        );
        remoteFn.call(this, keyword);
      }
    },

    // --------------------- 事件处理 end ------------------//

    // --------------------- 以下为组件支持外部调用的API方法 begin ------------------//
    /* 提示：用户可自行扩充这些方法！！！ */

    getFormRef() {
      /* 获取VFrom引用，必须在VForm组件created之后方可调用 */
      return this.refList['v_form_ref'];
    },
    getWriteState() {
      /** true 为编辑即后端已经该了表单值，  false 为提交后端已经给了值*/
      return this.writeState;
    },
    waitGetWidgetRef(widgetName) {
      let foundRef = this.refList[widgetName];
      if (foundRef == null) {
        return this.waitGetWidgetRef(widgetName);
      }
      return foundRef;
    },
    getWidgetRef(widgetName, showError) {
      let foundRef = this.refList[widgetName];
      if (!foundRef && !!showError) {
        this.$message.error(this.i18nt('render.hint.refNotFound') + widgetName);
      }
      return foundRef;
    },
    getWidgetOtherRef(formName, widgetName) {
      return this.refList['all_form_ref'][formName][widgetName];
    },

    getFieldEditor() {
      // 获取内置的el表单组件
      return this.$refs['fieldEditor'];
    },

    /*
      注意：VFormRender的setFormData方法不会触发子表单内field-widget的setValue方法，
      因为setFormData方法调用后，子表单内所有field-widget组件已被清空，接收不到setFormData事件！！
    * */
    setValue(newValue) {
      if (this.field.formItemFlag) {
        let oldValue = deepClone(this.fieldModel);
        this.$set(this, 'oldFieldValue', oldValue);
        this.$set(this, 'fieldModel', newValue);
        this.syncUpdateFormModel(newValue);
        this.initFileList();
      }
    },
    // 改变组件值,会触发onchange事件
    changeValue(newValue) {
      if (this.field.formItemFlag) {
        let oldValue = deepClone(this.fieldModel);
        this.$set(this, 'fieldModel', newValue);
        // this.fieldModel = newValue

        // this.$forceUpdate()
        // console.log(this.fieldModel)

        this.initFileList();

        this.syncUpdateFormModel(newValue);
        this.emitFieldDataChange(newValue, oldValue);
      }
    },
    getValue() {
      /* if ((this.field.type === 'picture-upload') || (this.field.type === 'file-upload')) {
        return this.fileList
      } else */
      return this.fieldModel;
    },
    getSelectValue() {
      return typeof this.oldFieldValue === 'string'
        ? this.oldFieldValue
        : this.oldFieldValue.join(',');
    },
    setSelectValue(val) {
      val = typeof val === 'string' ? val.replace(/\s+/g, '').split(',') : val;
      if (!this.field.options.multiple) {
        val = val[0];
      }
      this.setValue(val);
    },
    getFieldName() {
      return this.fieldName ? this.fieldName : '';
    },
    // 清空组件值
    resetField(flag = false) {
      let defaultValue = this.field.options.defaultValue;
      if (!flag) this.disableChangeValidate(); /* 禁用字段校验 */
      this.setValue(defaultValue);
      this.resetFileList();
      this.$nextTick(() => {
        if (!flag) this.enableChangeValidate(); /* 开启字段校验 */
      });
    },
    resetFileList() {
      if (this.field.type == 'file-upload') {
        this.clearFiles();
      }
      if (this.field.type == 'data-table') {
        this.clearCurrentSelectData();
        this.copyData = [];
        this.total = 0;
      }
      if (this.field.type == 'checkbox') {
      }
    },
    setWidgetOption(optionName, optionValue) {
      // 通用组件选项修改API
      if (this.field.options.hasOwnProperty(optionName)) {
        this.field.options[optionName] = optionValue;
        // TODO: 是否重新构建组件？？有些属性修改后必须重新构建组件才能生效，比如字段校验规则。
      }
    },

    setReadonly(flag) {
      this.field.options.readonly = flag;
    },

    setDisabled(flag) {
      this.field.options.disabled = flag;
    },

    setAppendButtonVisible(flag) {
      this.field.options.appendButton = flag;
    },

    setAppendButtonDisabled(flag) {
      this.field.options.appendButtonDisabled = flag;
    },

    setHidden(flag) {
      this.field.options.hidden = flag;
    },

    setRequired(flag) {
      this.field.options.required = flag;
      this.buildFieldRules();
    },

    setLabel(newLabel) {
      this.field.options.label = newLabel;
    },

    getLabel() {
      return this.field.options.label;
    },

    focus() {
      if (!!this.getFieldEditor() && !!this.getFieldEditor().focus) {
        this.getFieldEditor().focus();
      }
    },

    validate() {
      let result = false;
      this.getFormRef().$refs.renderForm.validateField(
        this.field.options.name,
        (errorMessage) => {
          result = errorMessage;
        },
      );
      return result;
    },
    clearSelectedOptions() {
      // 清空已选选项
      if (
        this.field.type !== 'checkbox' &&
        this.field.type !== 'radio' &&
        this.field.type !== 'select'
      ) {
        return;
      }

      if (
        this.field.type === 'checkbox' ||
        (this.field.type === 'select' && this.field.options.multiple)
      ) {
        this.fieldModel = [];
      } else {
        this.fieldModel = '';
      }
    },

    /**
     * 加载选项，并清空字段值
     * @param options
     */
    loadOptions(options) {
      this.field.options.optionItems = deepClone(options);
      this.clearSelectedOptions(); // 清空已选选项
    },

    /**
     * 重新加载选项，不清空字段值
     * @param options
     */
    reloadOptions(options) {
      this.field.options.optionItems = deepClone(options);
    },

    disableOption(optionValue) {
      this.disableOptionOfList(this.field.options.optionItems, optionValue);
    },

    enableOption(optionValue) {
      this.enableOptionOfList(this.field.options.optionItems, optionValue);
    },

    setUploadHeader(name, value) {
      this.$set(this.uploadHeaders, name, value);
    },

    setUploadData(name, value) {
      this.$set(this.uploadData, name, value);
    },

    setToolbar(customToolbar) {
      this.customToolbar = customToolbar;
    },

    // 获取参数配置
    getRequestParam() {
      let param = {};
      if (this.field.options.paramConfig) {
        JSON.parse(this.field.options.paramConfig)
          .filter((item) => item.value.length)
          .forEach((item) => {
            if (item.type === 'keyValue') {
              if (item.value[1].includes('.')) {
                // 获取弹窗内的值
                if (item.value[1].includes('dialog')) {
                  let keys = item.value[1].split('.');
                  param[item.key] = this.formModel[keys[keys.length - 1]];
                } else {
                  let containerForm = item.value[1].split('.')[0];
                  let form = item.value[1].split('.')[1];
                  // 当前点击的button的子表单中的索引
                  let temp = this.formModel[containerForm];
                  if (temp) {
                    temp = temp[this.subFormRowIndex][form];
                    param[item.key] = temp;
                  }
                }
              } else {
                if (
                  item.value[1].includes('select') &&
                  Array.isArray(this.formModel[item.value[1]])
                ) {
                  param[item.key] = this.formModel[item.value[1]].join();
                } else {
                  param[item.key] = this.formModel[item.value[1]] || '';
                }
              }
            } else {
              if (item.value[1].includes('customcondition')) {
                param[item.key] = this.formModel[item.value[1]];
              } else {
                param[item.key] = [];
                // 取所有
                let relation = {};
                item.subset.forEach((item2) => {
                  if (item2.value.length) {
                    relation[item2.value[1].split('.')[1]] =
                      item2.key.split('.')[1];
                  }
                });
                let dataAssemble = (data) => {
                  let obj = {};
                  for (let i in data) {
                    if (relation[i]) {
                      obj[relation[i]] = data[i];
                    }
                  }
                  return obj;
                };

                this.formModel[item.value[1]].forEach((item2, index) => {
                  if (this.checkedIndex.indexOf(index) > -1) {
                    param[item.key].push(dataAssemble(item2));
                  }
                });
              }
            }
          });
      }

      return param;
    },

    // --------------------- 以上为组件支持外部调用的API方法 end ------------------//

    getParentIndexItem() {
      // 分两种逻辑,一种是父级本来就是iteration或者data-table,一种是在上面很多层
      let type = this?.parentWidget?.type;
      let id = this?.parentWidget?.id;
      if (
        this.parentWidget &&
        type &&
        ['iteration', 'data-table'].includes(type)
      ) {
        if (type === 'iteration') {
          return {
            index: this.subFormRowIndex,
            item: this.getWidgetRef(id).getValue(),
          };
        }
        if (type === 'data-table') {
          return {
            index: this.subFormRowIndex,
            item: this.getWidgetRef(id).getData()[this.subFormRowIndex],
          };
        } else {
          console.error('父组件不是迭代组件，无法获取父组件的迭代索引项');
        }
      } else {
        console.log('getParentIndexItem');
        const getNodeWidget = (data) => {
          let count = 0;
          let currentNode = null;

          const bfs = (root) => {
            let type = root.$parent?.widget?.type;
            if (type && ['iteration', 'data-table'].includes(type)) {
              currentNode = root;
            } else {
              count++;
              if (count < 20 && !currentNode) {
                bfs(root.$parent);
              }
            }
          };
          bfs(data);

          return currentNode;
        };

        let currentNode = getNodeWidget(this);
        let parentNode = currentNode.$parent;
        if (!currentNode) return null;
        let index = currentNode.$attrs.subFormRowIndex;
        if (index === undefined) index = this.subFormRowIndex;
        if (parentNode.widget.type === 'iteration') {
          return {
            index,
            item: this.getWidgetRef(parentNode.widget.id).getValue(),
          };
        }
        if (parentNode.widget.type === 'data-table') {
          return {
            index,
            item: this.getWidgetRef(parentNode.widget.id).getData()[index],
          };
        } else {
          console.error('父组件不是迭代组件，无法获取父组件的迭代索引项');
        }
      }
    },

    $getValue(id) {
      return this.getWidgetRef(id) && this.getWidgetRef(id).getValue();
    },

    $setValue(id, value) {
      if (this.getWidgetRef(id)) {
        this.getWidgetRef(id).setValue(value);
      }
    },
  },
  mounted() {
    if (
      this.field &&
      this.field.options.hasOwnProperty('api') &&
      !this.field.options.hasOwnProperty('autoLoadData')
    ) {
      this.$set(this.field.options, 'autoLoadData', true);
    }
  },
};
