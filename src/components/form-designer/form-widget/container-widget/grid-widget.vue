<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <el-row
      :key="widget.id"
      :gutter="widget.options.gutter"
      class="grid-container"
      :class="[selected ? 'selected' : '', customClass]"
      @click.native.stop="selectWidget(widget)"
      :style="{
        margin: marginComp,
        padding: paddingComp,
        border: `${
          (widget.options.border2 ? widget.options.border2.width || 0 : 0) +
          'px'
        } ${widget.options.border2 ? widget.options.border2.type : 'none'} ${
          widget.options.border2 ? widget.options.border2.color || '#000' : ''
        }`,
      }"
    >
      <template v-for="(colWidget, colIdx) in widget.cols">
        <grid-col-widget
          :widget="colWidget"
          :designer="designer"
          :key="colWidget.id"
          :parent-list="widget.cols"
          :index-of-parent-list="colIdx"
          :parent-widget="widget"
        ></grid-col-widget>
      </template>
    </el-row>
  </container-wrapper>
</template>

<script>
import i18n from '@/utils/i18n';
import GridColWidget from '@/components/form-designer/form-widget/container-widget/grid-col-widget.vue';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper.vue';

export default {
  name: 'grid-widget',
  componentName: 'ContainerWidget',
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    GridColWidget,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
    marginComp() {
      const { margin } = this.widget.options;
      if (margin) {
        return `${margin.mt || 0}px ${margin.mr || 0}px ${margin.mb || 0}px ${
          margin.ml || 0
        }px `;
      }
      return 0;
    },
    paddingComp() {
      const { padding } = this.widget.options;
      if (padding) {
        return `${padding.pt || 0}px ${padding.pr || 0}px ${
          padding.pb || 0
        }px ${padding.pl || 0}px `;
      }
      return 0;
    },
  },
  watch: {
    //
  },
  mounted() {
    //
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.el-row.grid-container {
  min-height: 50px;
  //line-height: 48px;
  //padding: 6px;
  outline: 1px dashed #336699;

  .form-widget-list {
    min-height: 28px;
  }
}

.grid-container.selected,
.grid-cell.selected {
  outline: 2px solid #409eff !important;
}
</style>
