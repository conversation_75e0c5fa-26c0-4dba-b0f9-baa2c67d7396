/**
 * 装饰器
 * 类似于后端的注解+切面编程
 * https://juejin.cn/post/6946398860604342286#heading-10
 */

/**
 * 防抖，连续操作时，只在最后一次触发
 * @export
 * @param {Function} fun - 运行函数
 * @param {Number} wait - 延迟时间
 * @returns
 */
export function debounce(wait = 500) {
  return function(target, name, descriptor) {
    const fn = descriptor.value
    let timer = null
    descriptor.value = function() {
      const _this = this._isVue ? this : target
      clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(_this, arguments)
      }, wait)
    }
  }
}

/**
 * 节流，一定时间内，只能触发一次操作
 * @export
 * @param {Function} fn - 运行函数
 * @param {Number} wait - 延迟时间
 * @returns
 */
export function throttle(wait = 500) {
  return function(target, name, descriptor) {
    const fn = descriptor.value
    let canRun = true
    descriptor.value = function() {
      const _this = this._isVue ? this : target
      if (!canRun) return
      fn.apply(_this, arguments)
      canRun = false
      setTimeout(() => {
        canRun = true
      }, wait)
    }
  }
}

/**
 * 表单校验
 * @param {String} formElKey - 表单el
 */
export const formValidation = (formElKey = 'formEl') => {
  return (target, name, descriptor) => {
    const method = descriptor.value
    descriptor.value = async function() {
      const _this = this._isVue ? this : target
      const isValidate = await _this.$refs[formElKey].validate().catch(e => {})
      return isValidate && method.apply(_this, arguments)
    }
  }
}

/**
 * 确认框
 * @param {String} title - 标题
 * @param {String} content - 内容
 * @param {String} confirmButtonText - 确认按钮名称
 * @returns
 */
export const confirm = (params = { title: '标题', content: '内容', confirmButtonText: '确定', cancelButtonText: '取消' }) => {
  return (target, name, descriptor) => {
    const method = descriptor.value
    descriptor.value = async function(...args) {
      const _this = this._isVue ? this : target
      const { title, content, confirmButtonText, cancelButtonText } = params
      const flag = await this.$confirm(content, title, {
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        center: true,
        customClass: 'messageBox-prompt-btn'
      }).catch(() => {})
      return flag && method.apply(_this, arguments)
    }
  }
}

/**
 * 自动开启loading
 * @export
 * @param {string} [loadingKey='loading']
 * @returns
 */
export function autoSwitch(loadingKey = 'loading') {
  return function(target, name, descriptor) {
    const method = descriptor.value
    descriptor.value = async function() {
      const _this = this._isVue ? this : target
      _this[loadingKey] = true // 开启
      const [err, result] = await to(method.apply(_this, arguments))
      _this[loadingKey] = false // 关闭
      return err || result
    }
  }
}
