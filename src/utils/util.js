import Clipboard from 'clipboard';

export function isNull(value) {
  return value === null || value === undefined;
}

export function isNotNull(value) {
  return value !== null && value !== undefined;
}

export function isEmptyStr(str) {
  //return (str === undefined) || (!str) || (!/[^\s]/.test(str));
  return (
    str === undefined ||
    (!str && str !== 0 && str !== '0') ||
    !/[^\s]/.test(str)
  );
}

export const generateId = function () {
  return Math.floor(
    Math.random() * 100000 + Math.random() * 20000 + Math.random() * 5000,
  );
};

export const deepClone = function (origin) {
  if (origin === undefined) {
    return undefined;
  }

  return JSON.parse(JSON.stringify(origin));
};

export const overwriteObj = function (obj1, obj2) {
  /* 浅拷贝对象属性，obj2覆盖obj1 */
  // for (let prop in obj2) {
  //   if (obj2.hasOwnProperty(prop)) {
  //     obj1[prop] = obj2[prop]
  //   }
  // }

  Object.keys(obj2).forEach((prop) => {
    obj1[prop] = obj2[prop];
  });
};

export const addWindowResizeHandler = function (handler) {
  let oldHandler = window.onresize;
  if (typeof window.onresize != 'function') {
    window.onresize = handler;
  } else {
    window.onresize = function () {
      oldHandler();
      handler();
    };
  }
};

const createStyleSheet = function () {
  let head = document.head || document.getElementsByTagName('head')[0];
  let style = document.createElement('style');
  style.type = 'text/css';
  head.appendChild(style);
  return style.sheet;
};

export const insertCustomCssToHead = function (cssCode) {
  let head = document.getElementsByTagName('head')[0];
  let oldStyle = document.getElementById('vform-custom-css');
  if (!!oldStyle) {
    head.removeChild(oldStyle); //应该先清除后插入！！
  }

  let newStyle = document.createElement('style');
  newStyle.type = 'text/css';
  newStyle.rel = 'stylesheet';
  newStyle.id = 'vform-custom-css';
  try {
    newStyle.appendChild(document.createTextNode(cssCode));
  } catch (ex) {
    newStyle.styleSheet.cssText = cssCode;
  }

  head.appendChild(newStyle);
};

export const insertGlobalFunctionsToHtml = function (functionsCode) {
  let bodyEle = document.getElementsByTagName('body')[0];
  let oldScriptEle = document.getElementById('v_form_global_functions');
  !!oldScriptEle && bodyEle.removeChild(oldScriptEle);

  let newScriptEle = document.createElement('script');
  newScriptEle.id = 'v_form_global_functions';
  newScriptEle.type = 'text/javascript';
  newScriptEle.innerHTML = functionsCode;
  bodyEle.appendChild(newScriptEle);
};

export const optionExists = function (optionsObj, optionName) {
  if (!optionsObj) {
    return false;
  }

  return Object.keys(optionsObj).indexOf(optionName) > -1;
};

export const loadRemoteScript = function (srcPath, callback) {
  /*加载远程js，加载成功后执行回调函数*/
  let sid = encodeURIComponent(srcPath);
  let oldScriptEle = document.getElementById(sid);

  if (!oldScriptEle) {
    let s = document.createElement('script');
    s.src = srcPath;
    s.id = sid;
    document.body.appendChild(s);

    s.onload = s.onreadystatechange = function (_, isAbort) {
      /* 借鉴自ace.js */
      if (
        isAbort ||
        !s.readyState ||
        s.readyState === 'loaded' ||
        s.readyState === 'complete'
      ) {
        s = s.onload = s.onreadystatechange = null;
        if (!isAbort) {
          callback();
        }
      }
    };
  }
};

export function traverseFieldWidgets(widgetList, handler) {
  widgetList.map((w) => {
    if (w.formItemFlag) {
      handler(w);
    } else if (w.type === 'grid') {
      w.cols.map((col) => {
        traverseFieldWidgets(col.widgetList, handler);
      });
    } else if (w.type === 'table') {
      w.rows.map((row) => {
        row.cols.map((cell) => {
          traverseFieldWidgets(cell.widgetList, handler);
        });
      });
    } else if (w.type === 'tab') {
      w.tabs.map((tab) => {
        traverseFieldWidgets(tab.widgetList, handler);
      });
    } else if (w.type === 'sub-form') {
      traverseFieldWidgets(w.widgetList, handler);
    } else if (w.category === 'container') {
      //自定义容器
      traverseFieldWidgets(w.widgetList, handler);
    }
  });
}

export function traverseContainWidgets(widgetList, handler) {
  widgetList.map((w) => {
    if (w.category === 'container') {
      handler(w);
    }

    if (w.type === 'grid') {
      w.cols.map((col) => {
        traverseContainWidgets(col.widgetList, handler);
      });
    } else if (w.type === 'table') {
      w.rows.map((row) => {
        row.cols.map((cell) => {
          traverseContainWidgets(cell.widgetList, handler);
        });
      });
    } else if (w.type === 'tab') {
      w.tabs.map((tab) => {
        traverseContainWidgets(tab.widgetList, handler);
      });
    } else if (w.type === 'sub-form') {
      traverseContainWidgets(w.widgetList, handler);
    } else if (w.type === 'trends-tab') {
      traverseContainWidgets(w.widgetList, handler);
    } else if (w.category === 'container') {
      //自定义容器
      traverseContainWidgets(w.widgetList, handler);
    }
  });
}

export function traverseAllWidgets(widgetList, handler) {
  widgetList.forEach((w) => {
    handler(w);

    if (w.type === 'grid') {
      w.cols.forEach((col) => {
        handler(col);
        traverseAllWidgets(col.widgetList, handler);
      });
    } else if (w.type === 'table') {
      w.rows.forEach((row) => {
        row.cols.forEach((cell) => {
          handler(cell);
          traverseAllWidgets(cell.widgetList, handler);
        });
      });
    } else if (w.type === 'tab') {
      w.tabs.forEach((tab) => {
        traverseAllWidgets(tab.widgetList, handler);
      });
    } else if (w.type === 'sub-form') {
      traverseAllWidgets(w.widgetList, handler);
    } else if (w.type === 'trends-tab') {
      traverseAllWidgets(w.widgetList, handler);
    } else if (w.type === 'collapse') {
      traverseAllWidgets(w.items, handler);
    } else if (w.category === 'container') {
      //自定义容器
      traverseAllWidgets(w.widgetList, handler);
    }
  });
}

function handleWidgetForTraverse(widget, handler) {
  if (!!widget.category) {
    traverseFieldWidgetsOfContainer(widget, handler);
  } else if (widget.formItemFlag) {
    handler(widget);
  }
}

/**
 * 遍历容器内的字段组件
 * @param con
 * @param handler
 */
export function traverseFieldWidgetsOfContainer(con, handler) {
  if (con.type === 'grid') {
    con.cols.forEach((col) => {
      col.widgetList.forEach((cw) => {
        handleWidgetForTraverse(cw, handler);
      });
    });
  } else if (con.type === 'table') {
    con.rows.forEach((row) => {
      row.cols.forEach((cell) => {
        cell.widgetList.forEach((cw) => {
          handleWidgetForTraverse(cw, handler);
        });
      });
    });
  } else if (con.type === 'tab') {
    con.tabs.forEach((tab) => {
      tab.widgetList.forEach((cw) => {
        handleWidgetForTraverse(cw, handler);
      });
    });
  } else if (con.type === 'sub-form') {
    con.widgetList.forEach((cw) => {
      handleWidgetForTraverse(cw, handler);
    });
  } else if (con.type === 'trends-tab') {
    con.widgetList.forEach((cw) => {
      handleWidgetForTraverse(cw, handler);
    });
  } else if (con.category === 'container') {
    //自定义容器
    con.widgetList.forEach((cw) => {
      handleWidgetForTraverse(cw, handler);
    });
  }
}

/**
 * 获取所有字段组件
 * @param widgetList
 * @returns {[]}
 */
export function getAllFieldWidgets(widgetList) {
  let result = [];
  let handlerFn = (w) => {
    result.push({
      type: w.type,
      name: w.options.name,
      field: w,
    });
  };
  traverseFieldWidgets(widgetList, handlerFn);

  return result;
}

/**
 * 获取所有容器组件
 * @param widgetList
 * @returns {[]}
 */
export function getAllContainerWidgets(widgetList) {
  let result = [];
  let handlerFn = (w) => {
    result.push({
      type: w.type,
      name: w.options.name,
      container: w,
    });
  };
  traverseContainWidgets(widgetList, handlerFn);

  return result;
}

export function copyToClipboard(
  content,
  clickEvent,
  $message,
  successMsg,
  errorMsg,
) {
  const clipboard = new Clipboard(clickEvent.target, {
    text: () => content,
  });

  clipboard.on('success', () => {
    $message.success(successMsg);
    clipboard.destroy();
  });

  clipboard.on('error', () => {
    $message.error(errorMsg);
    clipboard.destroy();
  });

  clipboard.onClick(clickEvent);
}

export function getQueryParam(variable) {
  let query = window.location.search.substring(1);
  let vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split('=');
    if (pair[0] == variable) {
      return pair[1];
    }
  }

  return undefined;
}

export const filterSearchTree = (nodes, predicate, wrapMatchFn = () => false) => {
  // 如果已经没有节点了，结束递归
  if (!(nodes && nodes.length)) {
    return []
  }
  const newChildren = []
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i]
    // 想要截止匹配的那一层（如果有匹配的则不用递归了，直接取下面所有的子节点）
    if (wrapMatchFn(node) && predicate(node)) {
      newChildren.push(node)
      continue
    }
    const subs = filterSearchTree(node.children, predicate, wrapMatchFn)

    // 以下两个条件任何一个成立，当前节点都应该加入到新子节点集中
    // 1. 子孙节点中存在符合条件的，即 subs 数组中有值
    // 2. 自己本身符合条件
    if ((subs && subs.length) || predicate(node)) {
      node.children = subs || []
      newChildren.push(node)
    }
  }
  return newChildren.length ? newChildren : []
}

export function uuid() {
  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
