(function($, window, document, undefined) {
  var pluginName = 'table2excel'

  var defaults = {
    exclude: '.noExl',
    name: 'Table2Excel',
    tdWidth: '100px',
    tdHeight: '30px'
  }

  function Plugin(element, options) {
    this.element = $(element).clone()
    this.settings = $.extend({}, defaults, options)
    this._defaults = defaults
    this._name = pluginName
    this.init()
  }

  Plugin.prototype = {
    init: function() {
      var e = this
      var headStyle = ' <style type="text/css">' +
                '#exportHeadSettingsid td{ border: 1px solid #000000;width: ' + e.settings.tdWidth + ';height: ' + e.settings.tdHeight + '; text-align: center;}' +
                '</style>'

      e.template = {
        head: '<html  xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets>',
        sheet: {
          head: '<x:ExcelWorksheet><x:Name>' + e.settings.filename,
          tail: '</x:Name><x:WorksheetOptions><x:Print><x:ValidPrinterInfo /></x:Print><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>'
        },
        mid: '</x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' + headStyle + '</head><body>',
        table: {
          head: "<table style='text-align: center;'>",
          tail: '</table>'
        },
        foot: '</body></html>'
      }

      e.tableRows = []

      // Styling variables
      // var additionalStyles = ''
      // var compStyle = null

      // get contents of table except for exclude
      $(e.element).each(function(i, o) {
        console.log($(o))
        var tempRows = ''
        $(o).find(' > div:not(.el-table__fixed, .el-table__fixed-right) tr').not(e.settings.exclude).each(function(i, p) {
          // style='" + additionalStyles + "'
          tempRows += '<tr style="text-align: center;" >'

          // Loop through each TH and TD
          $(p).find('td,th').not(e.settings.exclude).each(function(i, q) { // p did not exist, I corrected
            var rc = {
              rows: $(this).attr('rowspan'),
              cols: $(this).attr('colspan'),
              flag: $(q).find(e.settings.exclude)
            }
            if (rc.flag.length > 0) {
              tempRows += '<td style="border: 1px solid #000;"> </td>' // exclude it!!
            } else {
              tempRows += '<td'
              if (rc.rows > 0) {
                tempRows += " rowspan='" + rc.rows + "' "
              }
              if (rc.cols > 0) {
                tempRows += " colspan='" + rc.cols + "' "
              }
              tempRows += '>' + $(q).html() + '</td>'
            }
          })

          tempRows += '</tr>'
        })
        // exclude img tags
        if (e.settings.exclude_img) {
          tempRows = exclude_img(tempRows)
        }

        // exclude link tags
        if (e.settings.exclude_links) {
          tempRows = exclude_links(tempRows)
        }

        // exclude input tags
        if (e.settings.exclude_inputs) {
          tempRows = exclude_inputs(tempRows)
        }
        console.log(tempRows)
        e.tableRows.push(tempRows)
      })

      e.tableToExcel(e.tableRows, e.settings.name, e.settings.sheetName)
    },
    tableToExcel: function(table, name) {

      var e = this; var fullTemplate = ''; var i; var link; var a
      e.uri = 'data:application/vnd.ms-excel;base64,'
      e.base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
      }
      e.format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
          return c[p]
        })
      }
      e.ctx = {
        worksheet: name || 'Worksheet',
        table: table
      }

      fullTemplate = e.template.head

      if ($.isArray(table)) {
        for (i in table) {
          fullTemplate += e.template.sheet.head + 'Sheet' + i + '' + e.template.sheet.tail
        }
      }

      fullTemplate += e.template.mid
      if ($.isArray(table)) {
        for (i in table) {
          fullTemplate += e.template.table.head + '{table' + i + '}' + e.template.table.tail
        }
      }

      fullTemplate += e.template.foot

      for (i in table) {
        e.ctx['table' + i] = table[i]
      }
      delete e.ctx.table

      if (typeof msie !== 'undefined' && msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) // If Internet Explorer
      {
        if (typeof Blob !== 'undefined') {
          fullTemplate = [fullTemplate]
          var blob1 = new Blob(fullTemplate, { type: 'text/html' })
          window.navigator.msSaveBlob(blob1, getFileName(e.settings))
        } else {
          txtArea1.document.open('text/html', 'replace')
          txtArea1.document.write(fullTemplate)
          txtArea1.document.close()
          txtArea1.focus()
          sa = txtArea1.document.execCommand('SaveAs', true, getFileName(e.settings))
        }
      } else {
        link = e.uri + e.base64(e.format(fullTemplate, e.ctx))
        a = document.createElement('a')
        a.download = getFileName(e.settings)
        a.href = link
        a.click()
      }

      return true
    }
  }

  function getFileName(settings) {
    return (settings.filename ? settings.filename : 'table2excel') + '.xls'
  }

  function exclude_img(string) {
    return string.replace(/<img[^>]*>/gi, '')
  }

  function exclude_links(string) {
    return string.replace(/<A[^>]*>|<\/A>/g, '')
  }

  function exclude_inputs(string) {
    return string.replace(/<input[^>]*>|<\/input>/gi, '')
  }

  $.fn[ pluginName ] = function(options) {
    var e = this
    e.each(function() {
      if (!$.data(e, 'plugin_' + pluginName)) {
        $.data(e, 'plugin_' + pluginName, new Plugin(this, options))
      }
    })

    return e
  }
})(jQuery, window, document)
