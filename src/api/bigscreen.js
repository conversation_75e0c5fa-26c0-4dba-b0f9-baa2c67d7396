import request from '@/utils/request'

// 保存大屏设计
export function insertDashboard(data) {
  return request({
    url: '/interfaces/reportDashboard',
    method: 'post',
    data,
  })
}

// 预览、查询大屏详情
export function detailDashboard(id) {
  return request({
    url: '/interfaces/reportDashboard/' + id,
    method: 'get',
  })
}

// 数据集查询
export function queryAllDataSet(data) {
  return request({
    url: 'dataSet/queryAllDataSet',
    method: 'get',
  })
}



//  获取对应数据集的列集合
export function detail(data) {
  return request({
    url: 'dataSet/detailBysetId/' + data,
    method: 'get',
  })
}

//  根据reportCode获取报表表格详情
export function detailByReportCode(data) {
  return request({
    url: '/interfaces/excel/detailByReportCode/' + data,
    method: 'get',
  })
}
// reportExcel
export function addReportExcel (data) {
  return request({
    url: '/interfaces/excel',
    method: 'post',
    data,
  })
}

// reportExcel
export function editReportExcel (data) {
  return request({
    url: 'interfaces/excel',
    method: 'put',
    data,
  })
}

// 预览报表，渲染数据
export function preview(data) {
  return request({
    url: 'interfaces/reportExcel/preview',
    method: 'post',
    data,
  })
}

// 导出报表
export function exportExcel(data) {
  return request({
    url: '/interfaces/reportExcel/exportExcel',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
// 获取数据集信息
export function detailBysetId(data) {
  return request({
    url: 'dataSet/detailBysetId/' + data,
    method: 'get',
  })
}

// 获取动态数据
export function getData(data) {
  return request({
    url: 'reportDashboard/getData',
    method: 'post',
    data,
  })
}



export function reportList(params) {
  return request({
    url: '/interfaces/report/pageList',
    method: 'GET',
    params,
  })
}
// report
export function reportPageList (params) {
  return request({
    url: '/report/pageList',
    method: 'get',
    params,
  })
}
export function getDictList (type) {
  return request({
    url: `/interfaces/dict/select/${type}`,
    method: 'get',
  })
}

// 查询所有数据字典接口
export function getAllDict() {
  return request({
    url: '/interfaces/dict/all',
    method: 'GET',
  })
}

export function initDictToLocalstorage(callback) {
  getAllDict().then((res) => {
    if (res.code != 200) {
      console.error('初始化数据字典到local storage失败: ' + res.message)
      return
    }

    // 保存数据字典到localStorage
    setStorageItem('AJReportDict', res.data)
    if (callback != null) {
      callback()
    }
  })
}
