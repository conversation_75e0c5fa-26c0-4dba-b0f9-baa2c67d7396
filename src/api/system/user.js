import request from '@/utils/request'
import { praseStrEmpty } from '@/utils/param.js'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/interfaces/DRLine/user/getUser?userId=' + praseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/interfaces/DRLine/user/add',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/interfaces/DRLine/user/edit',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password, version) {
  const data = {
    userId,
    password,
    version
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}

/**
 * 查询科室人员树
 */
export function getUserTree() {
  return request({
    url: '/system/user/deptUserTree',
    method: 'get'
  })
}
// 查询用户详细
export function getByIdInfo(userId) {
  return request({
    url: `/system/user/getByIdInfo/${userId}`,
    method: 'get'
  })
}
// 查询部门详细
export function getBydeptIdInfo(deptId) {
  return request({
    url: `/system/dept/getByIdInfo/${deptId}`,
    method: 'get'
  })
}

export function getUnreadMessage(query) {
  return request({
    url: '/system/message/unread',
    method: 'get',
    params: query
  })
}
export function readMessage(data) {
  return request({
    url: '/system/message/read',
    method: 'PUT',
    data: data
  })
}
export function noticeMessage(data) {
  return request({
    url: '/system/message/notice/read',
    method: 'PUT',
    data: data
  })
}
export function getfeedback(query) {
  return request({
    url: '/system/feedback/list',
    method: 'get',
    params: query
  })
}
// 修改反馈回复意见
export function updateFeedback(data) {
  return request({
    url: '/system/feedback',
    method: 'PUT',
    data: data
  })
}
// 删除反馈回复
export function delfeedback(ids) {
  return request({
    url: '/system/feedback/' + ids,
    method: 'delete'
  })
}
export function getUserAvatars(data) {
  return request({
    url: '/system/user/getUserAvatars',
    method: 'post',
    data: data
  })
}
