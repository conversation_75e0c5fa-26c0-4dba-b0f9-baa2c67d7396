import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function treeselect(params) {
  return request({
    url: '/system/dept/treeselect',
    method: 'get',
    params: params
  })
}
// 设备树
export function deviceTreeSelect() {
  return request({
    url: '/system/tree/getTree/4',
    method: 'get'
  })
}

/**
 * 获取部门仪表下拉树列表
 */
export function getMeterTree() {
  return request({
    url: '/system/dept/treemesterselect',
    method: 'get'
  })
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: '/system/dept/roleDeptTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/dept',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/dept',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'delete'
  })
}
// 设备批量写入数据下拉列表
export function getDeptSelect() {
  return request({
    url: '/equipment/devicebase/getTreeRegin',
    method: 'get'
  })
}
// 获取部门设备下拉树列表
export function getEquipmentDept() {
  return request({
    url: '/system/dept/treeDeviceSelect',
    method: 'get'
  })
}
// 获取点检主单状态列表
export function getdeviceCheckDept(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query
  })
}
// 获取点检子单状态列表
export function getCheckSubOrder(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query
  })
}
// 获取业务类型下拉列表
export function getSpareUseType(query) {
  return request({
    url: '/equipment/pareUseLog/getSpareUseType',
    method: 'get',
    params: query
  })
}
// 树类型下拉字典
export function getTreeTypeDict(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query
  })
}
// 设备台账树bom
export function getTreeBom(query) {
  return request({
    url: '/system/tree/device/bom/list',
    method: 'get',
    params: query
  })
}
// 获取自定义树treeId
export function getTreeId(dicId) {
  return request({
    url: '/equipment/devicebase/treeSelect/' + dicId,
    method: 'get'
  })
}
// 获取自定义树的节点类型
export function getNodeType(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query
  })
}
// 根据节点类型id获取节点数据
export function getTreeNodeValue(treeSubKey) {
  return request({
    url: '/system/tree/getTreeSubKeyData/' + treeSubKey,
    method: 'get'
  })
}
// 设备类型字典
export function getEquipmentType(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query
  })
}

/**
 * 获取部门设备树列表
 * @param {Object} params
 */
export function getTreeDevice(params) {
  return request({
    url: '/system/dept/treeDeviceSelect',
    method: 'get',
    params: params
  })
}

/**
 * 获取仪表下拉树列表
 * @param {Object} params
 */
export function getTreeMeter(params) {
  return request({
    url: '/system/dept/treeMeterSelect',
    method: 'get',
    params: params
  })
}

/**
 * 获取成都仪表下拉树列表
 * @param {Object} params
 */
 export function getCdTreeMeter(params) {
  return request({
    // url: '/system/dept/treeMeterSelect',
    url: '/interfaces/goldcupEnergy/getCdMeterTree',
    method: 'get',
    params: params
  })
}
// 备件类型字典
export function getTypeOfSpareParts(params) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: params
  })
}
// 备件等级字典
export function getSparePartsGrade(params) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: params
  })
}
// 部门类型字典
export function getDepartmentType(params) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: params
  })
}
// 功能位置下拉列表
export function getLocation(params) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: params
  })
}

// 获取人员树
export function getTreeUser(params) {
  return request({
    url: '/system/tree/user',
    method: 'get',
    params: params
  })
}
