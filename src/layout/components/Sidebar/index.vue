<template>
  <div>
    <div class="siderbar">
      <logo v-if="showLogo" :collapse="isCollapse" />
      <el-scrollbar
        :class="settings.sideTheme"
        wrap-class="scrollbar-wrapper"
        style="height: calc(100vh - 83px)"
      >
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          :collapse-transition="false"
          mode="vertical"
        >
          <sidebar-item
            v-for="(route, index) in sidebarRouters"
            :key="route.path + index"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo.vue";
import SidebarItem from "./SidebarItem.vue";
import variables from "@/assets/styles/variables.scss";
import { constantRoutes } from "@/router";
export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar", "topbarRouters"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === "/") {
              router.children[item].path =
                "/redirect/" + router.children[item].path;
            } else {
              if (!this.ishttp(router.children[item].path)) {
                router.children[item].path =
                  router.path + "/" + router.children[item].path;
              }
            }
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
  },
  mounted() {
    let routes = [];
    let key = "/" + this.activeMenu.split("/")[1];
    if (key == "/formDesign" || key == "/flow" || key == "/flowFormStart") {
      key = "/tool";
    }
    if (this.childrenMenus && this.childrenMenus.length > 0) {
      this.childrenMenus.map((item) => {
        if (key === item.parentPath || (key === "index" && item.path === "")) {
          routes.push(item);
        }
      });
    }

    if (routes.length > 0) {
      this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
    }
  },
  methods: {
    ishttp(url) {
      return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
    },
  },
};
</script>
<style scoped lang="scss">
.siderbar {
  background-image: url("../../../assets/images/菜单栏底部背景.png");
  background-repeat: no-repeat;
  background-position: center bottom;
  width: 100%;
  height: 100%;
}

</style>
