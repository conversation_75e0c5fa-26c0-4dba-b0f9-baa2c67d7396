<template>
  <span>
    <img
      v-if="icon && icon !== '#' && parseInt(icon) > 0"
      :src="getIconImage(icon)"
      style="width: 15px; height: 15px;margin-right: 16px; margin-left: 0.35em;"
    />
    <svg-icon
      v-else-if="icon && icon !== '#'"
      :icon-class="icon"
    />
    <span v-if="title" slot="title">{{ title }}</span>
  </span>
</template>

<script>
import { getIconImage } from '@/utils/index.js'
export default {
  name: 'MenuItem',
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  methods: {
    getIconImage
  }
}
</script>
