<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <div class="d-flex j-center a-center" style="height: 100%">
          <img
            v-if="user.avatar"
            src="../../../assets/images/logo.png"
            class="hide-logo"
          />
          <div
            v-else
            class="sidebar-title"
            :style="{
              color:
                sideTheme === 'theme-dark'
                  ? variables.sidebarTitle
                  : variables.sidebarLightTitle,
            }"
          >
            {{ title }}
          </div>
        </div>
        <!-- <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 v-else class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.sidebarTitle : variables.sidebarLightTitle }">{{ title }} </h1> -->
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <div class="d-flex flex-column a-center" style="height: 100%">
          <img
            v-if="user.avatar"
            src="../../../assets/images/logo.png"
            class="sidebar-logo"
          />
          <h1 class="sidebar-title">
            {{ title }}
          </h1>
          <img
            src="../../../assets/images/菜单栏顶部图片.png"
            class="img"
            alt=""
          />
        </div>
      </router-link>
    </transition>
    <img class="top-light" v-if="!collapse" src="../../../assets/images/logo顶部.png">
  </div>
</template>

<script>
// import logoImg from '@/assets/logo/logo.png'
import variables from "@/assets/styles/variables.scss";
import { mapGetters } from "vuex";

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme;
    },
    title() {
      return (
        sessionStorage.getItem("docTitle") || this.$store.state.app.appTitle
      );
    },
    ...mapGetters(["user"]),
  },
  data() {
    return {
      // title: '生产智控平台',
      // logo: logoImg
    };
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 130px;
  line-height: 50px;
  padding-top: 20px;
  background: transparent;
  text-align: center;
  overflow: hidden;
  .top-light{
    position: absolute;
    top: 0;
    width: 240px;
    left: 0;
    height: 27px;
  }
  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    & .hide-logo{
      width: 50px;
      height: 40px;
    }
    & .sidebar-logo {
      width: 88px;
      height: 40px;
    }

    & .sidebar-title {
      font-size: 20px;
      margin: 10px 0;
      font-weight: bold;
      color: #fefefe;
      line-height: 22px;

      background: linear-gradient(0deg, #cff1ff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    & .img {
      width: 190px;
      height: 17px;
    }
  }
}
</style>
