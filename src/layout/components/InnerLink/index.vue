<template>
  <div v-if="!link" class="error-page">404</div>
  <div v-else :style="containerStyle">
    <iframe
      :src="link"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto"
    ></iframe>
  </div>
</template>

<script>
export default {
  computed: {
    link() {
      return this.$route?.meta?.link || "";
    },
    containerStyle() {
      const height = document.documentElement.clientHeight - 94.5 + "px";
      return { height: height };
    }
  }
};
</script>
