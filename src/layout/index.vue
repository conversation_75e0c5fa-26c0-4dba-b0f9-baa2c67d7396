<template>
  <div
    :class="classObj"
    class="app-wrapper clearfix"
    :style="{ '--current-color': theme }"
  >
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar
      class="sidebar-container"
    />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <!--
          右侧抽屉更改为系统切换
          1. 注释掉 v-if="showSettings" 使其拥有过渡效果
          2. settings有有两种写法
             2.1 ./components/Settings/index.vue          系统设置组件
             2.2 ./components/Settings/SystemSwitch.vue   系统切换组件
       -->
      <right-panel>
        <settings />
      </right-panel>
    </div>
    <updatePassword ref="updatePasswordRef" />
  </div>
</template>

<script>
import RightPanel from '@/components/RightPanel/index.vue';
import { AppMain, Navbar, Sidebar, TagsView } from './components';
import ResizeMixin from './mixin/ResizeHandler';
import { mapState } from 'vuex';
import variables from '@/assets/styles/variables.scss';
import Settings from './components/Settings/SystemSwitch.vue';
import updatePassword from '@/components/updatePassword.vue';
export default {
  name: 'Layout',
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    updatePassword,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile',
      };
    },
    variables() {
      return variables;
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mixin.scss';
@import '@/assets/styles/variables.scss';

.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  background: url('../assets/images/主页背景.png') 100% 100% no-repeat;
  background-size: cover;
  color: #fefefe;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 2000;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 60px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
